# Arif AI Assistant 🤖

[![NPM Version](https://img.shields.io/npm/v/@inkbytefo/arif-ai)](https://www.npmjs.com/package/@inkbytefo/arif-ai)
[![NPM Downloads](https://img.shields.io/npm/dm/@inkbytefo/arif-ai)](https://www.npmjs.com/package/@inkbytefo/arif-ai)
[![License](https://img.shields.io/badge/license-Apache%202.0-blue.svg)](LICENSE)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.8-blue.svg)](https://www.typescriptlang.org/)

> **Akıllı AI asistanınız Arif, Türkçe dil desteği ile! 🇹🇷**

Arif AI Assistant, kodunuzu anlayan, projelerinizi geliştiren ve iş akışlarınızı otomatikleştiren gelişmiş bir AI CLI aracıdır. Modern mimari, gelişmiş çoklu sağlayıcı desteği ve Türkçe dil desteği ile tasarlanmıştır.

## ✨ Arif AI'nin Özellikleri

- 🇹🇷 **Türkçe Dil Desteği** - Tam Türkçe arayüz ve etkileşim
- 🏗️ **Modern Mimari** - Temiz, modüler tasarım ve ayrılmış sorumluluklar
- 🔌 **Gelişmiş Sağlayıcı Sistemi** - Plugin tabanlı AI sağlayıcı mimarisi
- ⚡ **Geliştirilmiş Performans** - Optimize edilmiş bellek kullanımı ve hızlı başlatma
- 🛠️ **Akıllı Yapılandırma** - Hiyerarşik config sistemi ve doğrulama
- 🎨 **Gelişmiş Arayüz** - İyileştirilmiş terminal arayüzü ve temalar
- 📚 **Kapsamlı Dokümantasyon** - Detaylı kılavuzlar ve API dokümantasyonu
- 🧪 **Güvenilir Test Sistemi** - %90+ kapsama sahip kapsamlı test paketi

### 🤖 Agentic System (Alpha)
- **Otonom Ajanlar** - Kendi kendini yöneten AI ajanları
- **MCP Entegrasyonu** - Model Context Protocol desteği
- **Gelişmiş Orkestrasyon** - Karmaşık iş akışları yönetimi
- **Güvenlik Korumaları** - Kapsamlı güvenlik ve güvenlik önlemleri
- **Performans İzleme** - Gerçek zamanlı metrikler ve analiz

## 🚀 Hızlı Başlangıç

```bash
# Hemen deneyin
npx @inkbytefo/arif-ai

# Veya global olarak kurun
npm install -g @inkbytefo/arif-ai
arif
```

**Yapılandırmayı başlatın:**

```bash
# Etkileşimli yapılandırma kurulumu
arif config init

# Veya API anahtarlarını manuel olarak ayarlayın
export OPENAI_API_KEY="your_key"
export ANTHROPIC_API_KEY="your_key"
export GOOGLE_API_KEY="your_key"
export MISTRAL_API_KEY="your_key"
export OPENROUTER_API_KEY="your_key"
```

## 🤖 Desteklenen AI Sağlayıcıları

| Provider | Models | Features | Status |
|----------|--------|----------|--------|
| **OpenAI** | GPT-4, GPT-3.5, GPT-4 Turbo | Chat, Streaming, Function Calls | ✅ Fully Supported |
| **Anthropic** | Claude 3 (Opus, Sonnet, Haiku) | Chat, Streaming, Function Calls | ✅ Fully Supported |
| **Google Gemini** | Gemini Pro, Gemini Flash | Chat, Streaming, Vision | ✅ Fully Supported |
| **Mistral** | Mistral Large, Medium, Small | Chat, Streaming, Function Calls | ✅ Fully Supported |
| **OpenRouter** | 100+ models from multiple providers | Chat, Streaming, All Features | ✅ Fully Supported |
| **Custom** | Your own endpoints | Configurable | ✅ Fully Supported |
| **Local Models** | Ollama, LocalAI | Chat, Streaming | 🚧 Coming Soon |

### 🌟 OpenRouter Highlights
- **100+ Models**: Access GPT-4, Claude, Gemini, Llama, and more through one API
- **Cost Effective**: Pay-per-use pricing with competitive rates
- **High Availability**: Automatic failover between providers
- **Latest Models**: Access to newest models as soon as they're released

## 💡 What Can You Do?

```bash
s647
> "Create a React component from this design mockup"
> "Analyze this codebase and suggest improvements"
> "Help me debug this authentication issue"
> "Generate unit tests for this function"
> "Refactor this code to use modern patterns"
> "Create documentation for this API"
```

## 🤖 Agentic System (Alpha)

Arif AI artık Anthropic'in en iyi uygulamalarına dayalı otonom ajan yetenekleri ile geliştirildi. Bu gelişmiş sistem, Model Context Protocol (MCP) entegrasyonu ve kapsamlı güvenlik önlemleri içerir.

### 🎯 Temel Özellikler

#### Otonom Ajanlar
- **Kendi kendini yöneten ajanlar** - Planlama, yürütme ve izleme yetenekleri
- **Çoklu ajan koordinasyonu** - Karmaşık iş akışları için ajan işbirliği
- **Bellek yönetimi** - Sürekli öğrenme için hafıza ve yansıma
- **Hata kurtarma** - Otomatik hata düzeltme ve uyarlanabilir davranış

#### Model Context Protocol (MCP)
- **Standartlaştırılmış araç entegrasyonu** - MCP spesifikasyonlarına uygun
- **İstemci/Sunucu mimarisi** - Ölçeklenebilir araç orkestrasyonu
- **Kaynak ve prompt yönetimi** - Gelişmiş AI etkileşimleri
- **Platformlar arası uyumluluk** - MCP uyumlu sistemlerle çalışma

#### Gelişmiş Araç Orkestrasyonu
- **İş akışı oluşturucu** - Karmaşık otomasyon için akıcı API
- **Paralel ve sıralı yürütme** - Bağımlılık yönetimi ile
- **Koşullu mantık** - Hata işleme ve karar verme
- **Performans izleme** - Optimizasyon ve analiz

### 🚀 Agentic System Kullanımı

```bash
# Otonom ajanlarla hedef yürütme
arif execute "Kod tabanını analiz et ve güvenlik raporu oluştur"

# Belirli bir ajan kullanma
arif execute --agent code-specialist "Kimlik doğrulama modülünü incele"

# İş akışları oluşturma ve çalıştırma
arif workflow create code-review --steps analyze,security,report
arif workflow execute code-review --input ./src

# Sistem performansını izleme
arif monitor status
arif monitor report --last 24h
```

### 📚 Agentic System Dokümantasyonu

- **[Agentic System Guide](AGENTIC_SYSTEM_GUIDE.md)** - Kapsamlı kullanım kılavuzu
- **[Alpha Test Plan](ALPHA_TEST_PLAN.md)** - Test planı ve senaryoları
- **[Deployment Guide](DEPLOYMENT_GUIDE.md)** - Kurulum ve dağıtım kılavuzu

## 🏗️ Architecture

S647 Refactored follows a clean, modular architecture:

```
packages/
├── core/                 # Core AI functionality
│   ├── ai/              # AI provider abstractions
│   ├── tools/           # Tool system
│   ├── config/          # Configuration management
│   ├── services/        # Business logic
│   └── utils/           # Utilities
├── cli/                 # Command-line interface
│   ├── commands/        # CLI commands
│   ├── ui/              # User interface
│   └── config/          # CLI configuration
└── shared/              # Shared utilities
    ├── types/           # TypeScript definitions
    ├── schemas/         # Validation schemas
    └── constants/       # Shared constants
```

## 📖 Documentation

- 📚 [User Guide](./docs/user-guide.md)
- 🏗️ [Architecture Guide](./docs/architecture.md)
- 🔧 [Configuration Reference](./docs/configuration.md)
- 🛠️ [Developer Guide](./docs/developer-guide.md)
- 🔌 [Provider Development](./docs/provider-development.md)
- 🧪 [Testing Guide](./docs/testing.md)

## 🔧 Configuration

S647 uses a hierarchical configuration system:

```json
{
  "providers": {
    "default": "openai",
    "openai": {
      "apiKey": "${OPENAI_API_KEY}",
      "model": "gpt-4",
      "temperature": 0.7
    },
    "anthropic": {
      "apiKey": "${ANTHROPIC_API_KEY}",
      "model": "claude-3-sonnet-20240229",
      "temperature": 0.7
    },
    "google": {
      "apiKey": "${GOOGLE_API_KEY}",
      "model": "gemini-pro",
      "temperature": 0.7
    },
    "mistral": {
      "apiKey": "${MISTRAL_API_KEY}",
      "model": "mistral-large-latest",
      "temperature": 0.7
    },
    "openrouter": {
      "apiKey": "${OPENROUTER_API_KEY}",
      "model": "openai/gpt-3.5-turbo",
      "siteName": "S647",
      "siteUrl": "https://s647.dev",
      "temperature": 0.7
    }
  },
  "tools": {
    "enabled": ["file", "git", "memory", "mcp"],
    "file": {
      "maxFileSize": "10MB",
      "allowedExtensions": [".js", ".ts", ".py", ".md"]
    }
  },
  "ui": {
    "theme": "dark",
    "animations": true,
    "verbose": false
  }
}
```

## 🛠️ Development

```bash
# Clone the repository
git clone https://github.com/inkbytefo/S647-refactored.git
cd S647-refactored

# Install dependencies
npm install

# Build the project
npm run build

# Run in development mode
npm run dev

# Run tests
npm test

# Run linting
npm run lint
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:ci

# Run integration tests
npm run test:integration

# Run E2E tests
npm run test:e2e
```

## 📦 Migration from v1.x

If you're upgrading from S647 v1.x, use our migration tool:

```bash
# Automatic migration
s647 migrate --from-version=1.x

# Manual migration guide
s647 migrate --guide
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Based on Google's Gemini CLI with significant enhancements
- Inspired by modern CLI tools and best practices
- Built with love by the open source community

---

**Built with ❤️ by [inkbytefo](https://github.com/inkbytefo)**

*Modern AI assistant for the modern developer*
