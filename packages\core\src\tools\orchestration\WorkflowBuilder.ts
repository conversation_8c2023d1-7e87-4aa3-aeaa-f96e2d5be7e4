/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type { ToolId, ToolParams } from '@inkbytefo/s647-shared';
import type { ToolExecutionPlan, ToolExecutionStep } from './ToolOrchestrator.js';

/**
 * Fluent builder for creating tool execution workflows
 * Implements Anthropic's workflow patterns
 */
export class WorkflowBuilder {
  private plan: Partial<ToolExecutionPlan>;
  private steps: ToolExecutionStep[] = [];
  private currentStep?: Partial<ToolExecutionStep>;

  constructor(id: string, name: string) {
    this.plan = {
      id,
      name,
      steps: []
    };
  }

  /**
   * Set workflow description
   */
  public description(desc: string): WorkflowBuilder {
    this.plan.description = desc;
    return this;
  }

  /**
   * Set workflow timeout
   */
  public timeout(ms: number): WorkflowBuilder {
    this.plan.timeout = ms;
    return this;
  }

  /**
   * Set max retries for the workflow
   */
  public maxRetries(retries: number): WorkflowBuilder {
    this.plan.maxRetries = retries;
    return this;
  }

  /**
   * Set workflow variables
   */
  public variables(vars: Record<string, any>): WorkflowBuilder {
    this.plan.variables = vars;
    return this;
  }

  /**
   * Add a tool step
   */
  public step(id: string, toolId: ToolId): StepBuilder {
    this.finalizePreviousStep();
    this.currentStep = {
      id,
      toolId,
      params: {}
    };
    return new StepBuilder(this);
  }

  /**
   * Create a prompt chaining workflow
   */
  public promptChain(steps: Array<{ id: string; toolId: ToolId; params?: ToolParams }>): WorkflowBuilder {
    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      const dependsOn = i > 0 ? [steps[i - 1].id] : undefined;
      
      this.steps.push({
        id: step.id,
        toolId: step.toolId,
        params: step.params || {},
        dependsOn
      });
    }
    return this;
  }

  /**
   * Create a parallel execution workflow
   */
  public parallel(steps: Array<{ id: string; toolId: ToolId; params?: ToolParams }>): WorkflowBuilder {
    for (const step of steps) {
      this.steps.push({
        id: step.id,
        toolId: step.toolId,
        params: step.params || {}
        // No dependencies - all run in parallel
      });
    }
    return this;
  }

  /**
   * Create a conditional workflow
   */
  public conditional(
    condition: string,
    trueSteps: Array<{ id: string; toolId: ToolId; params?: ToolParams }>,
    falseSteps?: Array<{ id: string; toolId: ToolId; params?: ToolParams }>
  ): WorkflowBuilder {
    // Add condition check step
    this.steps.push({
      id: 'condition_check',
      toolId: 'condition_evaluator',
      params: { condition }
    });

    // Add true branch steps
    for (const step of trueSteps) {
      this.steps.push({
        id: step.id,
        toolId: step.toolId,
        params: step.params || {},
        dependsOn: ['condition_check'],
        condition: `\${condition_check} === true`
      });
    }

    // Add false branch steps if provided
    if (falseSteps) {
      for (const step of falseSteps) {
        this.steps.push({
          id: step.id,
          toolId: step.toolId,
          params: step.params || {},
          dependsOn: ['condition_check'],
          condition: `\${condition_check} === false`
        });
      }
    }

    return this;
  }

  /**
   * Create an orchestrator-workers workflow
   */
  public orchestratorWorkers(
    orchestratorStep: { id: string; toolId: ToolId; params?: ToolParams },
    workerSteps: Array<{ id: string; toolId: ToolId; params?: ToolParams }>
  ): WorkflowBuilder {
    // Add orchestrator step
    this.steps.push({
      id: orchestratorStep.id,
      toolId: orchestratorStep.toolId,
      params: orchestratorStep.params || {}
    });

    // Add worker steps that depend on orchestrator
    for (const step of workerSteps) {
      this.steps.push({
        id: step.id,
        toolId: step.toolId,
        params: step.params || {},
        dependsOn: [orchestratorStep.id]
      });
    }

    return this;
  }

  /**
   * Create an evaluator-optimizer workflow
   */
  public evaluatorOptimizer(
    generatorStep: { id: string; toolId: ToolId; params?: ToolParams },
    evaluatorStep: { id: string; toolId: ToolId; params?: ToolParams },
    maxIterations: number = 3
  ): WorkflowBuilder {
    // Add initial generator step
    this.steps.push({
      id: generatorStep.id,
      toolId: generatorStep.toolId,
      params: generatorStep.params || {}
    });

    // Add evaluator-optimizer loop
    for (let i = 1; i <= maxIterations; i++) {
      const evalId = `${evaluatorStep.id}_${i}`;
      const genId = `${generatorStep.id}_${i}`;

      // Evaluator step
      this.steps.push({
        id: evalId,
        toolId: evaluatorStep.toolId,
        params: {
          ...evaluatorStep.params,
          input: i === 1 ? `\${${generatorStep.id}}` : `\${${generatorStep.id}_${i - 1}}`
        },
        dependsOn: i === 1 ? [generatorStep.id] : [`${generatorStep.id}_${i - 1}`]
      });

      // Generator step (if not last iteration)
      if (i < maxIterations) {
        this.steps.push({
          id: genId,
          toolId: generatorStep.toolId,
          params: {
            ...generatorStep.params,
            feedback: `\${${evalId}}`
          },
          dependsOn: [evalId],
          condition: `\${${evalId}.needsImprovement} === true`
        });
      }
    }

    return this;
  }

  /**
   * Finalize previous step
   */
  private finalizePreviousStep(): void {
    if (this.currentStep && this.currentStep.id && this.currentStep.toolId) {
      this.steps.push(this.currentStep as ToolExecutionStep);
      this.currentStep = undefined;
    }
  }

  /**
   * Build the workflow
   */
  public build(): ToolExecutionPlan {
    this.finalizePreviousStep();
    
    return {
      id: this.plan.id!,
      name: this.plan.name!,
      description: this.plan.description,
      steps: this.steps,
      variables: this.plan.variables,
      timeout: this.plan.timeout,
      maxRetries: this.plan.maxRetries
    };
  }

  /**
   * Internal method to add current step
   */
  public _addCurrentStep(step: ToolExecutionStep): void {
    this.steps.push(step);
    this.currentStep = undefined;
  }

  /**
   * Internal method to get current step
   */
  public _getCurrentStep(): Partial<ToolExecutionStep> | undefined {
    return this.currentStep;
  }

  /**
   * Internal method to set current step
   */
  public _setCurrentStep(step: Partial<ToolExecutionStep>): void {
    this.currentStep = step;
  }
}

/**
 * Step builder for configuring individual steps
 */
export class StepBuilder {
  private workflowBuilder: WorkflowBuilder;

  constructor(workflowBuilder: WorkflowBuilder) {
    this.workflowBuilder = workflowBuilder;
  }

  /**
   * Set step parameters
   */
  public params(params: ToolParams): StepBuilder {
    const currentStep = this.workflowBuilder._getCurrentStep();
    if (currentStep) {
      currentStep.params = params;
    }
    return this;
  }

  /**
   * Set step dependencies
   */
  public dependsOn(...stepIds: string[]): StepBuilder {
    const currentStep = this.workflowBuilder._getCurrentStep();
    if (currentStep) {
      currentStep.dependsOn = stepIds;
    }
    return this;
  }

  /**
   * Set step condition
   */
  public condition(condition: string): StepBuilder {
    const currentStep = this.workflowBuilder._getCurrentStep();
    if (currentStep) {
      currentStep.condition = condition;
    }
    return this;
  }

  /**
   * Set step retries
   */
  public retries(count: number): StepBuilder {
    const currentStep = this.workflowBuilder._getCurrentStep();
    if (currentStep) {
      currentStep.retries = count;
    }
    return this;
  }

  /**
   * Set step timeout
   */
  public timeout(ms: number): StepBuilder {
    const currentStep = this.workflowBuilder._getCurrentStep();
    if (currentStep) {
      currentStep.timeout = ms;
    }
    return this;
  }

  /**
   * Set success handler
   */
  public onSuccess(handler: string): StepBuilder {
    const currentStep = this.workflowBuilder._getCurrentStep();
    if (currentStep) {
      currentStep.onSuccess = handler;
    }
    return this;
  }

  /**
   * Set error handler
   */
  public onError(handler: string): StepBuilder {
    const currentStep = this.workflowBuilder._getCurrentStep();
    if (currentStep) {
      currentStep.onError = handler;
    }
    return this;
  }

  /**
   * Add another step
   */
  public step(id: string, toolId: ToolId): StepBuilder {
    return this.workflowBuilder.step(id, toolId);
  }

  /**
   * Build the workflow
   */
  public build(): ToolExecutionPlan {
    return this.workflowBuilder.build();
  }
}

/**
 * Utility functions for creating common workflow patterns
 */
export class WorkflowPatterns {
  /**
   * Create a simple sequential workflow
   */
  static sequential(
    id: string,
    name: string,
    steps: Array<{ id: string; toolId: ToolId; params?: ToolParams }>
  ): ToolExecutionPlan {
    return new WorkflowBuilder(id, name)
      .promptChain(steps)
      .build();
  }

  /**
   * Create a parallel workflow
   */
  static parallel(
    id: string,
    name: string,
    steps: Array<{ id: string; toolId: ToolId; params?: ToolParams }>
  ): ToolExecutionPlan {
    return new WorkflowBuilder(id, name)
      .parallel(steps)
      .build();
  }

  /**
   * Create a conditional workflow
   */
  static conditional(
    id: string,
    name: string,
    condition: string,
    trueSteps: Array<{ id: string; toolId: ToolId; params?: ToolParams }>,
    falseSteps?: Array<{ id: string; toolId: ToolId; params?: ToolParams }>
  ): ToolExecutionPlan {
    return new WorkflowBuilder(id, name)
      .conditional(condition, trueSteps, falseSteps)
      .build();
  }

  /**
   * Create a map-reduce workflow
   */
  static mapReduce(
    id: string,
    name: string,
    mapStep: { id: string; toolId: ToolId; params?: ToolParams },
    reduceStep: { id: string; toolId: ToolId; params?: ToolParams },
    items: any[]
  ): ToolExecutionPlan {
    const builder = new WorkflowBuilder(id, name);

    // Create map steps for each item
    const mapSteps = items.map((item, index) => ({
      id: `${mapStep.id}_${index}`,
      toolId: mapStep.toolId,
      params: { ...mapStep.params, item, index }
    }));

    // Add parallel map steps
    builder.parallel(mapSteps);

    // Add reduce step that depends on all map steps
    const mapStepIds = mapSteps.map(step => step.id);
    builder
      .step(reduceStep.id, reduceStep.toolId)
      .params({ ...reduceStep.params, mapResults: mapStepIds.map(id => `\${${id}}`) })
      .dependsOn(...mapStepIds);

    return builder.build();
  }

  /**
   * Create a retry workflow with exponential backoff
   */
  static retryWithBackoff(
    id: string,
    name: string,
    step: { id: string; toolId: ToolId; params?: ToolParams },
    maxRetries: number = 3
  ): ToolExecutionPlan {
    return new WorkflowBuilder(id, name)
      .step(step.id, step.toolId)
      .params(step.params || {})
      .retries(maxRetries)
      .timeout(30000)
      .build();
  }
}
