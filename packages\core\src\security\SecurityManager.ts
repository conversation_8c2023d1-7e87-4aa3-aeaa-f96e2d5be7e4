/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { EventEmitter } from 'events';
import type { Logger } from '@inkbytefo/s647-shared';

/**
 * Security threat levels
 */
export enum ThreatLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * Security violation types
 */
export enum ViolationType {
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  MALICIOUS_INPUT = 'malicious_input',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  SUSPICIOUS_BEHAVIOR = 'suspicious_behavior',
  DATA_EXFILTRATION = 'data_exfiltration',
  PRIVILEGE_ESCALATION = 'privilege_escalation',
  INJECTION_ATTACK = 'injection_attack',
  UNSAFE_OPERATION = 'unsafe_operation'
}

/**
 * Security violation
 */
export interface SecurityViolation {
  id: string;
  type: ViolationType;
  threatLevel: ThreatLevel;
  description: string;
  source: string;
  userId?: string;
  sessionId?: string;
  timestamp: Date;
  metadata: Record<string, any>;
  blocked: boolean;
  action: string;
}

/**
 * Security rule
 */
export interface SecurityRule {
  id: string;
  name: string;
  description: string;
  type: ViolationType;
  enabled: boolean;
  threatLevel: ThreatLevel;
  pattern?: string;
  validator?: (input: any, context: SecurityContext) => boolean;
  action: SecurityAction;
  metadata?: Record<string, any>;
}

/**
 * Security action
 */
export type SecurityAction = 
  | { type: 'block' }
  | { type: 'warn' }
  | { type: 'log' }
  | { type: 'rate_limit'; duration: number }
  | { type: 'quarantine'; duration: number }
  | { type: 'escalate'; to: string };

/**
 * Security context
 */
export interface SecurityContext {
  userId?: string;
  sessionId?: string;
  source: string;
  operation: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Rate limit configuration
 */
export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (context: SecurityContext) => string;
}

/**
 * Security manager configuration
 */
export interface SecurityManagerConfig {
  enabled: boolean;
  strictMode: boolean;
  defaultThreatLevel: ThreatLevel;
  rateLimiting: {
    enabled: boolean;
    global: RateLimitConfig;
    perUser: RateLimitConfig;
    perOperation: RateLimitConfig;
  };
  inputValidation: {
    enabled: boolean;
    maxInputLength: number;
    allowedCharacters?: RegExp;
    blockedPatterns: string[];
  };
  outputSanitization: {
    enabled: boolean;
    removeCredentials: boolean;
    removePaths: boolean;
    removeIPs: boolean;
  };
  auditLogging: {
    enabled: boolean;
    logLevel: 'all' | 'violations' | 'critical';
  };
}

/**
 * Comprehensive security manager for protecting the system
 */
export class SecurityManager extends EventEmitter {
  private config: SecurityManagerConfig;
  private logger?: Logger;
  private rules: Map<string, SecurityRule> = new Map();
  private violations: SecurityViolation[] = [];
  private rateLimiters: Map<string, RateLimiter> = new Map();
  private quarantinedUsers: Map<string, Date> = new Map();
  private suspiciousActivities: Map<string, SuspiciousActivity> = new Map();

  constructor(config: Partial<SecurityManagerConfig> = {}, logger?: Logger) {
    super();
    this.config = {
      enabled: true,
      strictMode: false,
      defaultThreatLevel: ThreatLevel.MEDIUM,
      rateLimiting: {
        enabled: true,
        global: { windowMs: 60000, maxRequests: 1000 },
        perUser: { windowMs: 60000, maxRequests: 100 },
        perOperation: { windowMs: 60000, maxRequests: 50 }
      },
      inputValidation: {
        enabled: true,
        maxInputLength: 100000,
        blockedPatterns: [
          '(?i)(script|javascript|vbscript)',
          '(?i)(eval|exec|system|shell)',
          '(?i)(drop|delete|truncate|alter)\\s+(table|database)',
          '(?i)(union|select|insert|update)\\s+.*\\s+(from|into)',
          '(?i)(<script|<iframe|<object|<embed)',
          '(?i)(file://|ftp://|data:)',
          '(?i)(\\.\\.[\\/\\\\]|[\\/\\\\]\\.\\.)',
          '(?i)(cmd\\.exe|powershell|bash|sh)',
          '(?i)(password|secret|token|key)\\s*[:=]'
        ]
      },
      outputSanitization: {
        enabled: true,
        removeCredentials: true,
        removePaths: true,
        removeIPs: true
      },
      auditLogging: {
        enabled: true,
        logLevel: 'violations'
      },
      ...config
    };
    this.logger = logger;
    this.setupDefaultRules();
  }

  /**
   * Validate input for security threats
   */
  public validateInput(
    input: any,
    context: SecurityContext
  ): { valid: boolean; violations: SecurityViolation[] } {
    if (!this.config.enabled) {
      return { valid: true, violations: [] };
    }

    const violations: SecurityViolation[] = [];

    // Check if user is quarantined
    if (context.userId && this.isUserQuarantined(context.userId)) {
      violations.push(this.createViolation(
        ViolationType.UNAUTHORIZED_ACCESS,
        ThreatLevel.HIGH,
        'User is quarantined',
        context,
        true,
        'quarantine_check'
      ));
    }

    // Check rate limits
    const rateLimitViolation = this.checkRateLimits(context);
    if (rateLimitViolation) {
      violations.push(rateLimitViolation);
    }

    // Validate input content
    if (typeof input === 'string') {
      const inputViolations = this.validateStringInput(input, context);
      violations.push(...inputViolations);
    } else if (typeof input === 'object') {
      const objectViolations = this.validateObjectInput(input, context);
      violations.push(...objectViolations);
    }

    // Check against custom rules
    const ruleViolations = this.checkSecurityRules(input, context);
    violations.push(...ruleViolations);

    // Track suspicious activity
    this.trackSuspiciousActivity(context, violations);

    // Log violations
    if (violations.length > 0) {
      this.logViolations(violations);
    }

    return {
      valid: violations.length === 0 || !violations.some(v => v.blocked),
      violations
    };
  }

  /**
   * Sanitize output to remove sensitive information
   */
  public sanitizeOutput(output: any, context: SecurityContext): any {
    if (!this.config.enabled || !this.config.outputSanitization.enabled) {
      return output;
    }

    if (typeof output === 'string') {
      return this.sanitizeString(output);
    } else if (Array.isArray(output)) {
      return output.map(item => this.sanitizeOutput(item, context));
    } else if (typeof output === 'object' && output !== null) {
      return this.sanitizeObject(output);
    }

    return output;
  }

  /**
   * Check if operation is safe to execute
   */
  public isOperationSafe(
    operation: string,
    params: any,
    context: SecurityContext
  ): { safe: boolean; reason?: string } {
    if (!this.config.enabled) {
      return { safe: true };
    }

    // Check for unsafe operations
    const unsafeOperations = [
      'eval',
      'exec',
      'system',
      'shell',
      'spawn',
      'fork',
      'require',
      'import'
    ];

    if (unsafeOperations.some(op => operation.toLowerCase().includes(op))) {
      return {
        safe: false,
        reason: `Operation '${operation}' is potentially unsafe`
      };
    }

    // Check parameters for unsafe content
    const validation = this.validateInput(params, context);
    if (!validation.valid) {
      return {
        safe: false,
        reason: 'Operation parameters contain security violations'
      };
    }

    return { safe: true };
  }

  /**
   * Validate string input
   */
  private validateStringInput(input: string, context: SecurityContext): SecurityViolation[] {
    const violations: SecurityViolation[] = [];

    // Check input length
    if (input.length > this.config.inputValidation.maxInputLength) {
      violations.push(this.createViolation(
        ViolationType.MALICIOUS_INPUT,
        ThreatLevel.MEDIUM,
        `Input exceeds maximum length (${input.length} > ${this.config.inputValidation.maxInputLength})`,
        context,
        this.config.strictMode,
        'length_check'
      ));
    }

    // Check against blocked patterns
    for (const pattern of this.config.inputValidation.blockedPatterns) {
      try {
        const regex = new RegExp(pattern);
        if (regex.test(input)) {
          violations.push(this.createViolation(
            ViolationType.INJECTION_ATTACK,
            ThreatLevel.HIGH,
            `Input matches blocked pattern: ${pattern}`,
            context,
            true,
            'pattern_check'
          ));
        }
      } catch (error) {
        this.logger?.warn(`Invalid regex pattern: ${pattern}`, error);
      }
    }

    // Check for suspicious character sequences
    const suspiciousPatterns = [
      /[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/, // Control characters
      /[^\x20-\x7E\s]/, // Non-printable characters (excluding whitespace)
      /(.)\1{50,}/, // Repeated characters (potential DoS)
      /%[0-9a-fA-F]{2}/, // URL encoding (potential bypass attempt)
      /\\u[0-9a-fA-F]{4}/, // Unicode escapes
      /\\x[0-9a-fA-F]{2}/, // Hex escapes
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(input)) {
        violations.push(this.createViolation(
          ViolationType.SUSPICIOUS_BEHAVIOR,
          ThreatLevel.MEDIUM,
          `Input contains suspicious character patterns`,
          context,
          this.config.strictMode,
          'character_check'
        ));
        break;
      }
    }

    return violations;
  }

  /**
   * Validate object input
   */
  private validateObjectInput(input: any, context: SecurityContext): SecurityViolation[] {
    const violations: SecurityViolation[] = [];

    try {
      const jsonString = JSON.stringify(input);
      
      // Check JSON size
      if (jsonString.length > this.config.inputValidation.maxInputLength) {
        violations.push(this.createViolation(
          ViolationType.MALICIOUS_INPUT,
          ThreatLevel.MEDIUM,
          `Object size exceeds maximum length`,
          context,
          this.config.strictMode,
          'object_size_check'
        ));
      }

      // Recursively validate string values
      const stringViolations = this.validateObjectStrings(input, context);
      violations.push(...stringViolations);

    } catch (error) {
      violations.push(this.createViolation(
        ViolationType.MALICIOUS_INPUT,
        ThreatLevel.HIGH,
        `Invalid object structure: ${(error as Error).message}`,
        context,
        true,
        'object_structure_check'
      ));
    }

    return violations;
  }

  /**
   * Recursively validate strings in object
   */
  private validateObjectStrings(obj: any, context: SecurityContext): SecurityViolation[] {
    const violations: SecurityViolation[] = [];

    if (typeof obj === 'string') {
      violations.push(...this.validateStringInput(obj, context));
    } else if (Array.isArray(obj)) {
      for (const item of obj) {
        violations.push(...this.validateObjectStrings(item, context));
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const value of Object.values(obj)) {
        violations.push(...this.validateObjectStrings(value, context));
      }
    }

    return violations;
  }

  /**
   * Check rate limits
   */
  private checkRateLimits(context: SecurityContext): SecurityViolation | null {
    if (!this.config.rateLimiting.enabled) {
      return null;
    }

    const checks = [
      { key: 'global', config: this.config.rateLimiting.global },
      { key: `user:${context.userId}`, config: this.config.rateLimiting.perUser },
      { key: `operation:${context.operation}`, config: this.config.rateLimiting.perOperation }
    ];

    for (const check of checks) {
      if (!check.config) continue;

      const limiter = this.getRateLimiter(check.key, check.config);
      if (!limiter.isAllowed()) {
        return this.createViolation(
          ViolationType.RATE_LIMIT_EXCEEDED,
          ThreatLevel.MEDIUM,
          `Rate limit exceeded for ${check.key}`,
          context,
          true,
          'rate_limit_check'
        );
      }
    }

    return null;
  }

  /**
   * Check security rules
   */
  private checkSecurityRules(input: any, context: SecurityContext): SecurityViolation[] {
    const violations: SecurityViolation[] = [];

    for (const rule of this.rules.values()) {
      if (!rule.enabled) continue;

      let violated = false;

      if (rule.pattern && typeof input === 'string') {
        try {
          const regex = new RegExp(rule.pattern);
          violated = regex.test(input);
        } catch (error) {
          this.logger?.warn(`Invalid rule pattern: ${rule.pattern}`, error);
          continue;
        }
      }

      if (rule.validator) {
        try {
          violated = !rule.validator(input, context);
        } catch (error) {
          this.logger?.warn(`Rule validator error: ${rule.id}`, error);
          continue;
        }
      }

      if (violated) {
        const blocked = rule.action.type === 'block' || 
                      (rule.action.type === 'rate_limit' && rule.threatLevel === ThreatLevel.HIGH);

        violations.push(this.createViolation(
          rule.type,
          rule.threatLevel,
          rule.description,
          context,
          blocked,
          `rule:${rule.id}`
        ));
      }
    }

    return violations;
  }

  /**
   * Create security violation
   */
  private createViolation(
    type: ViolationType,
    threatLevel: ThreatLevel,
    description: string,
    context: SecurityContext,
    blocked: boolean,
    action: string
  ): SecurityViolation {
    const violation: SecurityViolation = {
      id: `violation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      threatLevel,
      description,
      source: context.source,
      userId: context.userId,
      sessionId: context.sessionId,
      timestamp: new Date(),
      metadata: { ...context.metadata, operation: context.operation },
      blocked,
      action
    };

    this.violations.push(violation);
    this.emit('violation', violation);

    return violation;
  }

  /**
   * Get or create rate limiter
   */
  private getRateLimiter(key: string, config: RateLimitConfig): RateLimiter {
    if (!this.rateLimiters.has(key)) {
      this.rateLimiters.set(key, new RateLimiter(config));
    }
    return this.rateLimiters.get(key)!;
  }

  /**
   * Check if user is quarantined
   */
  private isUserQuarantined(userId: string): boolean {
    const quarantineEnd = this.quarantinedUsers.get(userId);
    if (!quarantineEnd) return false;

    if (Date.now() > quarantineEnd.getTime()) {
      this.quarantinedUsers.delete(userId);
      return false;
    }

    return true;
  }

  /**
   * Track suspicious activity
   */
  private trackSuspiciousActivity(context: SecurityContext, violations: SecurityViolation[]): void {
    if (violations.length === 0) return;

    const key = context.userId || context.sessionId || context.source;
    if (!key) return;

    let activity = this.suspiciousActivities.get(key);
    if (!activity) {
      activity = {
        key,
        violations: 0,
        lastViolation: new Date(),
        threatLevel: ThreatLevel.LOW
      };
      this.suspiciousActivities.set(key, activity);
    }

    activity.violations += violations.length;
    activity.lastViolation = new Date();

    // Escalate threat level based on violation count
    if (activity.violations >= 10) {
      activity.threatLevel = ThreatLevel.CRITICAL;
    } else if (activity.violations >= 5) {
      activity.threatLevel = ThreatLevel.HIGH;
    } else if (activity.violations >= 2) {
      activity.threatLevel = ThreatLevel.MEDIUM;
    }

    // Auto-quarantine for critical threat level
    if (activity.threatLevel === ThreatLevel.CRITICAL && context.userId) {
      this.quarantineUser(context.userId, 3600000); // 1 hour
    }
  }

  /**
   * Quarantine user
   */
  public quarantineUser(userId: string, durationMs: number): void {
    const quarantineEnd = new Date(Date.now() + durationMs);
    this.quarantinedUsers.set(userId, quarantineEnd);
    
    this.emit('userQuarantined', { userId, quarantineEnd });
    this.logger?.warn(`User quarantined: ${userId} until ${quarantineEnd}`);
  }

  /**
   * Sanitize string output
   */
  private sanitizeString(str: string): string {
    let sanitized = str;

    if (this.config.outputSanitization.removeCredentials) {
      // Remove potential credentials
      sanitized = sanitized.replace(
        /(password|secret|token|key|api[_-]?key)\s*[:=]\s*[^\s\n]+/gi,
        '$1: [REDACTED]'
      );
    }

    if (this.config.outputSanitization.removePaths) {
      // Remove file paths
      sanitized = sanitized.replace(
        /[a-zA-Z]:\\[^\s\n]+|\/[^\s\n]+/g,
        '[PATH_REDACTED]'
      );
    }

    if (this.config.outputSanitization.removeIPs) {
      // Remove IP addresses
      sanitized = sanitized.replace(
        /\b(?:\d{1,3}\.){3}\d{1,3}\b/g,
        '[IP_REDACTED]'
      );
    }

    return sanitized;
  }

  /**
   * Sanitize object output
   */
  private sanitizeObject(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeOutput(item, {} as SecurityContext));
    }

    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'string') {
        sanitized[key] = this.sanitizeString(value);
      } else {
        sanitized[key] = this.sanitizeOutput(value, {} as SecurityContext);
      }
    }

    return sanitized;
  }

  /**
   * Log violations
   */
  private logViolations(violations: SecurityViolation[]): void {
    if (!this.config.auditLogging.enabled) return;

    for (const violation of violations) {
      const shouldLog = this.config.auditLogging.logLevel === 'all' ||
                       (this.config.auditLogging.logLevel === 'violations') ||
                       (this.config.auditLogging.logLevel === 'critical' && 
                        violation.threatLevel === ThreatLevel.CRITICAL);

      if (shouldLog) {
        this.logger?.warn(`Security violation: ${violation.description}`, {
          violationId: violation.id,
          type: violation.type,
          threatLevel: violation.threatLevel,
          source: violation.source,
          userId: violation.userId,
          blocked: violation.blocked
        });
      }
    }
  }

  /**
   * Setup default security rules
   */
  private setupDefaultRules(): void {
    // SQL Injection detection
    this.addRule({
      id: 'sql-injection',
      name: 'SQL Injection Detection',
      description: 'Detects potential SQL injection attempts',
      type: ViolationType.INJECTION_ATTACK,
      enabled: true,
      threatLevel: ThreatLevel.HIGH,
      pattern: '(?i)(union|select|insert|update|delete|drop|create|alter)\\s+.*\\s+(from|into|table|database)',
      action: { type: 'block' }
    });

    // XSS detection
    this.addRule({
      id: 'xss-detection',
      name: 'XSS Detection',
      description: 'Detects potential XSS attempts',
      type: ViolationType.INJECTION_ATTACK,
      enabled: true,
      threatLevel: ThreatLevel.HIGH,
      pattern: '(?i)(<script|<iframe|<object|<embed|javascript:|vbscript:|data:)',
      action: { type: 'block' }
    });

    // Command injection detection
    this.addRule({
      id: 'command-injection',
      name: 'Command Injection Detection',
      description: 'Detects potential command injection attempts',
      type: ViolationType.INJECTION_ATTACK,
      enabled: true,
      threatLevel: ThreatLevel.CRITICAL,
      pattern: '(?i)(cmd\\.exe|powershell|bash|sh|eval|exec|system)\\s*[\\(\\[]',
      action: { type: 'block' }
    });

    this.logger?.debug('Default security rules loaded');
  }

  /**
   * Add security rule
   */
  public addRule(rule: SecurityRule): void {
    this.rules.set(rule.id, rule);
    this.logger?.debug(`Added security rule: ${rule.id}`);
  }

  /**
   * Remove security rule
   */
  public removeRule(ruleId: string): boolean {
    const removed = this.rules.delete(ruleId);
    if (removed) {
      this.logger?.debug(`Removed security rule: ${ruleId}`);
    }
    return removed;
  }

  /**
   * Get security statistics
   */
  public getSecurityStatistics(): {
    totalViolations: number;
    violationsByType: Record<ViolationType, number>;
    violationsByThreatLevel: Record<ThreatLevel, number>;
    quarantinedUsers: number;
    suspiciousActivities: number;
    activeRules: number;
  } {
    const violationsByType = {} as Record<ViolationType, number>;
    const violationsByThreatLevel = {} as Record<ThreatLevel, number>;

    for (const violation of this.violations) {
      violationsByType[violation.type] = (violationsByType[violation.type] || 0) + 1;
      violationsByThreatLevel[violation.threatLevel] = (violationsByThreatLevel[violation.threatLevel] || 0) + 1;
    }

    return {
      totalViolations: this.violations.length,
      violationsByType,
      violationsByThreatLevel,
      quarantinedUsers: this.quarantinedUsers.size,
      suspiciousActivities: this.suspiciousActivities.size,
      activeRules: Array.from(this.rules.values()).filter(r => r.enabled).length
    };
  }

  /**
   * Get recent violations
   */
  public getRecentViolations(limit: number = 100): SecurityViolation[] {
    return this.violations.slice(-limit);
  }

  /**
   * Clear old violations
   */
  public clearOldViolations(olderThanMs: number = 86400000): number {
    const cutoff = new Date(Date.now() - olderThanMs);
    const initialCount = this.violations.length;
    this.violations = this.violations.filter(v => v.timestamp >= cutoff);
    return initialCount - this.violations.length;
  }
}

/**
 * Rate limiter implementation
 */
class RateLimiter {
  private requests: number[] = [];
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = config;
  }

  public isAllowed(): boolean {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;

    // Remove old requests
    this.requests = this.requests.filter(time => time > windowStart);

    // Check if under limit
    if (this.requests.length < this.config.maxRequests) {
      this.requests.push(now);
      return true;
    }

    return false;
  }
}

/**
 * Suspicious activity tracking
 */
interface SuspiciousActivity {
  key: string;
  violations: number;
  lastViolation: Date;
  threatLevel: ThreatLevel;
}
