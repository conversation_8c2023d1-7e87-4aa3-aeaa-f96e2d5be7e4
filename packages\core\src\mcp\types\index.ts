/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type { 
  Tool as MCPTool,
  Resource as MCPResource,
  Prompt as <PERSON><PERSON>rompt,
  CallToolRequest,
  CallToolResult,
  ListToolsRequest,
  ListToolsResult,
  GetPromptRequest,
  GetPromptResult,
  ListPromptsRequest,
  ListPromptsResult,
  ReadResourceRequest,
  ReadResourceResult,
  ListResourcesRequest,
  ListResourcesResult,
  Transport,
  Client,
  Server
} from '@modelcontextprotocol/sdk/types.js';

// Re-export MCP SDK types
export type {
  MCPTool,
  MCPResource,
  MCPPrompt,
  CallToolRequest,
  CallToolResult,
  ListToolsRequest,
  ListToolsResult,
  GetPromptRequest,
  GetPromptResult,
  ListPromptsRequest,
  ListPromptsResult,
  ReadResourceRequest,
  ReadResourceResult,
  ListResourcesRequest,
  ListResourcesResult,
  Transport,
  Client,
  Server
};

// Custom MCP types for Arif AI
export interface MCPServerConfig {
  name: string;
  command: string;
  args?: string[];
  env?: Record<string, string>;
  transport: 'stdio' | 'sse';
  timeout?: number;
  retries?: number;
  enabled?: boolean;
}

export interface MCPClientConfig {
  servers: Record<string, MCPServerConfig>;
  defaultTimeout?: number;
  maxRetries?: number;
  enableLogging?: boolean;
}

export interface MCPServerInfo {
  name: string;
  version: string;
  description?: string;
  capabilities: {
    tools?: boolean;
    resources?: boolean;
    prompts?: boolean;
  };
  status: 'connected' | 'disconnected' | 'error';
  lastError?: string;
}

export interface MCPToolWrapper {
  serverId: string;
  tool: MCPTool;
  execute: (params: any) => Promise<CallToolResult>;
}

export interface MCPResourceWrapper {
  serverId: string;
  resource: MCPResource;
  read: () => Promise<ReadResourceResult>;
}

export interface MCPPromptWrapper {
  serverId: string;
  prompt: MCPPrompt;
  get: (args?: any) => Promise<GetPromptResult>;
}

export interface MCPAgentContext {
  servers: Map<string, MCPServerInfo>;
  tools: Map<string, MCPToolWrapper>;
  resources: Map<string, MCPResourceWrapper>;
  prompts: Map<string, MCPPromptWrapper>;
}

export interface MCPWorkflowStep {
  id: string;
  type: 'tool' | 'prompt' | 'resource' | 'condition' | 'parallel';
  serverId?: string;
  toolId?: string;
  promptId?: string;
  resourceId?: string;
  params?: any;
  condition?: string;
  steps?: MCPWorkflowStep[];
  onSuccess?: string;
  onError?: string;
}

export interface MCPWorkflow {
  id: string;
  name: string;
  description?: string;
  steps: MCPWorkflowStep[];
  variables?: Record<string, any>;
  timeout?: number;
}

export interface MCPAgentConfig {
  name: string;
  description?: string;
  systemPrompt?: string;
  workflows: MCPWorkflow[];
  tools: string[];
  resources: string[];
  prompts: string[];
  maxIterations?: number;
  timeout?: number;
  enableMemory?: boolean;
  memorySize?: number;
}

export interface MCPAgentState {
  id: string;
  status: 'idle' | 'thinking' | 'executing' | 'waiting' | 'error' | 'complete';
  currentWorkflow?: string;
  currentStep?: string;
  variables: Record<string, any>;
  memory: any[];
  iterations: number;
  startTime: Date;
  lastActivity: Date;
  error?: string;
}

export interface MCPAgentResult {
  success: boolean;
  result?: any;
  error?: string;
  steps: Array<{
    stepId: string;
    type: string;
    status: 'success' | 'error' | 'skipped';
    result?: any;
    error?: string;
    duration: number;
  }>;
  totalDuration: number;
  iterations: number;
}

// Event types for MCP system
export interface MCPEvent {
  type: string;
  timestamp: Date;
  data: any;
}

export interface MCPServerEvent extends MCPEvent {
  type: 'server.connected' | 'server.disconnected' | 'server.error';
  serverId: string;
}

export interface MCPToolEvent extends MCPEvent {
  type: 'tool.called' | 'tool.completed' | 'tool.error';
  serverId: string;
  toolId: string;
}

export interface MCPAgentEvent extends MCPEvent {
  type: 'agent.started' | 'agent.step' | 'agent.completed' | 'agent.error';
  agentId: string;
  workflowId?: string;
  stepId?: string;
}

export type MCPEventType = MCPServerEvent | MCPToolEvent | MCPAgentEvent;

// Error types
export class MCPError extends Error {
  constructor(
    message: string,
    public code: string,
    public serverId?: string,
    public cause?: Error
  ) {
    super(message);
    this.name = 'MCPError';
  }
}

export class MCPConnectionError extends MCPError {
  constructor(serverId: string, cause?: Error) {
    super(`Failed to connect to MCP server: ${serverId}`, 'CONNECTION_ERROR', serverId, cause);
    this.name = 'MCPConnectionError';
  }
}

export class MCPToolError extends MCPError {
  constructor(toolId: string, serverId: string, cause?: Error) {
    super(`Tool execution failed: ${toolId}`, 'TOOL_ERROR', serverId, cause);
    this.name = 'MCPToolError';
  }
}

export class MCPWorkflowError extends MCPError {
  constructor(workflowId: string, stepId?: string, cause?: Error) {
    super(`Workflow execution failed: ${workflowId}${stepId ? ` at step ${stepId}` : ''}`, 'WORKFLOW_ERROR', undefined, cause);
    this.name = 'MCPWorkflowError';
  }
}
