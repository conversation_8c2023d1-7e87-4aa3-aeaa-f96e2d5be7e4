# Arif AI Agentic System Guide

## 🎯 Overview

Arif AI has been enhanced with a comprehensive agentic system based on Anthropic's best practices and the Model Context Protocol (MCP). This guide covers the new autonomous agent capabilities, architecture, and usage.

## 🏗️ Architecture

### Core Components

1. **Model Context Protocol (MCP)**
   - Client/Server implementation for tool orchestration
   - Standardized communication between AI models and tools
   - Support for tools, resources, and prompts

2. **Autonomous Agent Framework**
   - Self-directing agents with planning capabilities
   - Execution monitoring and error recovery
   - Memory management and reflection

3. **Tool Orchestration System**
   - Advanced workflow management
   - Parallel and sequential execution
   - Conditional logic and error handling

4. **Security & Safety Guardrails**
   - Input validation and sanitization
   - Safe execution environments
   - Rate limiting and threat detection

5. **Performance Monitoring**
   - Real-time metrics collection
   - Performance analysis and alerting
   - Resource usage tracking

## 🤖 Agent Types

### 1. General Assistant Agent
- **ID**: `general`
- **Capabilities**: General purpose tasks, tool execution, workflow orchestration
- **Use Cases**: Basic automation, information gathering, simple workflows

### 2. Code Specialist Agent
- **ID**: `code-specialist`
- **Capabilities**: Code analysis, generation, debugging, testing
- **Use Cases**: Software development, code review, refactoring

### 3. File Operations Agent
- **ID**: `file-ops`
- **Capabilities**: File management, directory operations, search
- **Use Cases**: File system automation, data processing, content management

## 🔧 Getting Started

### 1. Initialize the Agentic System

```typescript
import { 
  AgentManager, 
  MCPClient, 
  ToolOrchestrator,
  SecurityManager,
  MetricsCollector,
  PerformanceMonitor 
} from '@inkbytefo/s647-core';

// Initialize core components
const metricsCollector = new MetricsCollector();
const securityManager = new SecurityManager();
const performanceMonitor = new PerformanceMonitor(metricsCollector);
const toolOrchestrator = new ToolOrchestrator(toolManager);

// Initialize MCP client
const mcpClient = new MCPClient({
  servers: {
    'local-tools': {
      name: 'Local Tools Server',
      command: 'node',
      args: ['mcp-server.js'],
      transport: 'stdio'
    }
  }
});

// Initialize agent manager
const agentManager = new AgentManager(
  aiManager,
  toolOrchestrator,
  { mcpClient }
);

// Register default agents
agentManager.registerDefaultAgents();
```

### 2. Execute Goals with Agents

```typescript
// Execute a goal with the best available agent
const result = await agentManager.executeGoalWithBestAgent(
  "Analyze the codebase and generate a summary report",
  {
    userId: "user123",
    sessionId: "session456"
  }
);

// Execute with a specific agent
const codeResult = await agentManager.executeGoal({
  agentId: "code-specialist",
  goal: "Review the authentication module for security issues",
  context: {
    userId: "user123",
    constraints: ["no-external-requests"],
    preferences: { format: "markdown" }
  }
});
```

### 3. Create Custom Workflows

```typescript
import { WorkflowBuilder, WorkflowPatterns } from '@inkbytefo/s647-core';

// Create a sequential workflow
const codeReviewWorkflow = new WorkflowBuilder('code-review', 'Code Review Workflow')
  .description('Automated code review process')
  .step('analyze', 'code_analyzer')
    .params({ path: '${input.path}', depth: 'full' })
    .retries(2)
  .step('security-check', 'security_scanner')
    .params({ code: '${analyze}' })
    .dependsOn('analyze')
  .step('generate-report', 'report_generator')
    .params({ 
      analysis: '${analyze}',
      security: '${security-check}',
      format: 'markdown'
    })
    .dependsOn('analyze', 'security-check')
  .build();

// Register and execute workflow
toolOrchestrator.registerPlan(codeReviewWorkflow);
const workflowResult = await toolOrchestrator.executePlan(
  'code-review',
  context,
  { input: { path: './src' } }
);
```

## 🛡️ Security Features

### Input Validation

```typescript
// Validate user input before processing
const validation = securityManager.validateInput(
  userInput,
  {
    userId: "user123",
    source: "web-interface",
    operation: "code-analysis",
    timestamp: new Date()
  }
);

if (!validation.valid) {
  console.log("Security violations:", validation.violations);
}
```

### Safe Execution Environment

```typescript
import { SafeExecutionEnvironment, PermissionLevel } from '@inkbytefo/s647-core';

const safeEnv = new SafeExecutionEnvironment(securityManager);

// Create execution context
const context = safeEnv.createExecutionContext(
  PermissionLevel.LIMITED,
  {
    allowedOperations: ['file_read', 'http_request'],
    resourceLimits: {
      maxMemoryMB: 256,
      maxExecutionTimeMs: 30000,
      allowedPaths: ['/tmp', '/workspace']
    },
    sandboxed: true
  }
);

// Execute operation safely
const result = await safeEnv.executeOperation(
  'file_read',
  { path: '/workspace/data.json' },
  context
);
```

## 📊 Monitoring & Analytics

### Performance Monitoring

```typescript
// Track agent performance
performanceMonitor.recordAgent({
  agentId: 'code-specialist',
  operation: 'code-analysis',
  duration: 5000,
  success: true,
  tokensUsed: 1500,
  cost: 0.05,
  timestamp: new Date()
});

// Get performance report
const report = performanceMonitor.getLatestReport();
console.log('Success rate:', report.summary.successRate);
console.log('Average response time:', report.summary.averageResponseTime);
```

### Telemetry Collection

```typescript
import { TelemetryManager } from '@inkbytefo/s647-core';

const telemetry = new TelemetryManager(metricsCollector, performanceMonitor);

// Track user actions
telemetry.trackUserAction('code-review-started', 'code-editor', 'user123');

// Track feature usage
telemetry.trackFeatureUsage('autonomous-agent', 'agent-manager', 'user123');

// Get usage analytics
const analytics = telemetry.getUsageAnalytics();
console.log('Total events:', analytics.totalEvents);
console.log('Unique users:', analytics.uniqueUsers);
```

## 🔄 Error Handling & Recovery

### Automatic Error Recovery

```typescript
import { ErrorHandler, RecoveryManager } from '@inkbytefo/s647-core';

const errorHandler = new ErrorHandler();
const recoveryManager = new RecoveryManager(errorHandler);

// Handle errors with automatic recovery
const result = await errorHandler.executeWithErrorHandling(
  async () => {
    // Your operation here
    return await riskyOperation();
  },
  {
    operation: 'data-processing',
    component: 'agent-executor',
    timestamp: new Date()
  },
  fallbackValue // Optional fallback value
);
```

### Custom Recovery Plans

```typescript
// Register custom recovery plan
recoveryManager.registerRecoveryPlan({
  id: 'api-failure-recovery',
  name: 'API Failure Recovery',
  description: 'Recovery plan for API failures',
  errorPattern: 'api|network|timeout',
  priority: 8,
  enabled: true,
  steps: [
    {
      id: 'retry-with-backoff',
      name: 'Retry with Exponential Backoff',
      type: 'automatic',
      action: { type: 'custom', handler: 'exponential-backoff' }
    },
    {
      id: 'switch-endpoint',
      name: 'Switch to Backup Endpoint',
      type: 'automatic',
      action: { type: 'switch_provider', provider: 'backup-api' }
    }
  ]
});
```

## 🧪 Testing

### Unit Tests

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:agents
npm run test:mcp
npm run test:security
npm run test:monitoring
```

### Integration Tests

```bash
# Run integration tests
npm run test:integration

# Run end-to-end tests
npm run test:e2e
```

### Performance Tests

```bash
# Run performance benchmarks
npm run test:performance

# Run load tests
npm run test:load
```

## 🚀 Deployment

### Development Environment

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Enable debug logging
DEBUG=arif:* npm run dev
```

### Production Environment

```bash
# Build for production
npm run build

# Start production server
npm start

# Enable telemetry (optional)
TELEMETRY_ENABLED=true npm start
```

### Docker Deployment

```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY dist ./dist
COPY config ./config

EXPOSE 3000
CMD ["npm", "start"]
```

## 📚 API Reference

### Agent Manager API

```typescript
interface AgentManager {
  // Register agents
  registerAgent(config: AgentConfig): void;
  unregisterAgent(agentId: string): boolean;
  
  // Execute goals
  executeGoal(request: AgentExecutionRequest): Promise<AgentExecutionResult>;
  executeGoalWithBestAgent(goal: string, context?: Partial<AgentExecutionContext>): Promise<AgentExecutionResult>;
  
  // Agent management
  getAvailableAgents(): AgentConfig[];
  getAgentState(agentId: string): AgentState | undefined;
  pauseAgent(agentId: string): boolean;
  resumeAgent(agentId: string): boolean;
  stopAgent(agentId: string): boolean;
}
```

### MCP Client API

```typescript
interface MCPClient {
  // Connection management
  initialize(): Promise<void>;
  connectToServer(serverId: string, config: MCPServerConfig): Promise<void>;
  disconnect(): Promise<void>;
  
  // Tool execution
  executeTool(toolId: string, params: any): Promise<CallToolResult>;
  readResource(resourceId: string): Promise<ReadResourceResult>;
  getPrompt(promptId: string, args?: any): Promise<GetPromptResult>;
  
  // Capabilities
  getAvailableTools(): MCPToolWrapper[];
  getAvailableResources(): MCPResourceWrapper[];
  getAvailablePrompts(): MCPPromptWrapper[];
}
```

## 🔧 Configuration

### Environment Variables

```bash
# Core settings
NODE_ENV=production
LOG_LEVEL=info
DEBUG=arif:*

# AI Provider settings
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_API_KEY=your_google_key

# Security settings
SECURITY_ENABLED=true
SECURITY_STRICT_MODE=false
RATE_LIMITING_ENABLED=true

# Monitoring settings
METRICS_ENABLED=true
TELEMETRY_ENABLED=false
PERFORMANCE_MONITORING=true

# MCP settings
MCP_ENABLED=true
MCP_SERVER_TIMEOUT=30000
```

### Configuration Files

```json
{
  "agents": {
    "enabled": true,
    "maxConcurrentExecutions": 10,
    "defaultTimeout": 300000,
    "enableMemory": true
  },
  "mcp": {
    "enabled": true,
    "servers": {
      "local-tools": {
        "command": "node",
        "args": ["mcp-server.js"],
        "transport": "stdio",
        "enabled": true
      }
    }
  },
  "security": {
    "enabled": true,
    "strictMode": false,
    "rateLimiting": {
      "enabled": true,
      "perUser": {
        "windowMs": 60000,
        "maxRequests": 100
      }
    }
  },
  "monitoring": {
    "metrics": {
      "enabled": true,
      "flushInterval": 60000
    },
    "telemetry": {
      "enabled": false,
      "anonymizeData": true
    }
  }
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Agent not responding**
   - Check agent status: `agentManager.getAgentState(agentId)`
   - Verify agent is not paused or stopped
   - Check resource limits and timeouts

2. **MCP connection failures**
   - Verify MCP server is running
   - Check server configuration and transport type
   - Review server logs for errors

3. **Security violations**
   - Review input validation rules
   - Check user permissions and rate limits
   - Examine security violation logs

4. **Performance issues**
   - Monitor resource usage and metrics
   - Check for memory leaks or high CPU usage
   - Review performance reports and alerts

### Debug Mode

```bash
# Enable debug logging
DEBUG=arif:agents,arif:mcp,arif:security npm start

# Enable verbose logging
LOG_LEVEL=debug npm start

# Enable performance profiling
NODE_ENV=development PROFILE=true npm start
```

## 📈 Best Practices

### Agent Design

1. **Keep agents focused** - Each agent should have a specific purpose
2. **Use appropriate permission levels** - Grant minimum necessary permissions
3. **Implement proper error handling** - Handle failures gracefully
4. **Monitor performance** - Track metrics and optimize bottlenecks

### Security

1. **Validate all inputs** - Never trust user input
2. **Use sandboxed execution** - Isolate potentially dangerous operations
3. **Implement rate limiting** - Prevent abuse and DoS attacks
4. **Monitor for threats** - Track suspicious activities

### Performance

1. **Use async operations** - Avoid blocking the event loop
2. **Implement caching** - Cache frequently accessed data
3. **Monitor resource usage** - Track memory and CPU usage
4. **Optimize workflows** - Use parallel execution where possible

## 🤝 Contributing

### Development Setup

```bash
# Clone repository
git clone https://github.com/inkbytefo/s647.git
cd s647

# Install dependencies
npm install

# Run tests
npm test

# Start development server
npm run dev
```

### Code Style

- Use TypeScript for all new code
- Follow existing code style and conventions
- Add comprehensive tests for new features
- Update documentation for API changes

### Pull Request Process

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests and documentation
5. Submit a pull request

## 📄 License

This project is licensed under the Apache 2.0 License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Anthropic](https://www.anthropic.com/) for agentic system best practices
- [Model Context Protocol](https://modelcontextprotocol.io/) for standardization
- The open-source community for inspiration and contributions
