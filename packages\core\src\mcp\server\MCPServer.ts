/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import type { Logger } from '@inkbytefo/s647-shared';
import type {
  MCPTool,
  MCPResource,
  MCPPrompt,
  CallToolRequest,
  ListToolsRequest,
  ListResourcesRequest,
  ListPromptsRequest,
  ReadResourceRequest,
  GetPromptRequest
} from '../types/index.js';
import { ToolManager } from '../../tools/manager/ToolManager.js';
import type { Tool, ToolParams, ToolContext } from '@inkbytefo/s647-shared';

/**
 * MCP Server implementation for Arif AI
 * Exposes Arif AI tools, resources, and prompts via MCP protocol
 */
export class MCPServer {
  private server: Server;
  private logger?: Logger;
  private toolManager: ToolManager;
  private resources: Map<string, MCPResource> = new Map();
  private prompts: Map<string, MCPPrompt> = new Map();

  constructor(
    name: string = 'arif-ai-mcp-server',
    version: string = '2.0.0',
    toolManager?: ToolManager,
    logger?: Logger
  ) {
    this.logger = logger;
    this.toolManager = toolManager || new ToolManager();

    this.server = new Server(
      {
        name,
        version
      },
      {
        capabilities: {
          tools: {},
          resources: {},
          prompts: {}
        }
      }
    );

    this.setupHandlers();
  }

  /**
   * Setup MCP request handlers
   */
  private setupHandlers(): void {
    // Tools handlers
    this.server.setRequestHandler(ListToolsRequest, async () => {
      const tools = this.toolManager.getAllTools();
      return {
        tools: tools.map(tool => this.convertToMCPTool(tool))
      };
    });

    this.server.setRequestHandler(CallToolRequest, async (request) => {
      const { name, arguments: args } = request.params;
      
      try {
        const tool = this.toolManager.getTool(name);
        if (!tool) {
          return {
            content: [
              {
                type: 'text',
                text: `Tool not found: ${name}`
              }
            ],
            isError: true
          };
        }

        // Create tool context
        const context: ToolContext = {
          workingDirectory: process.cwd(),
          environment: process.env,
          user: {
            id: 'mcp-user',
            name: 'MCP User'
          }
        };

        const result = await this.toolManager.executeTool(name, args as ToolParams, context);
        
        if (!result.success) {
          return {
            content: [
              {
                type: 'text',
                text: `Tool execution failed: ${result.error?.message || 'Unknown error'}`
              }
            ],
            isError: true
          };
        }

        return {
          content: [
            {
              type: 'text',
              text: typeof result.data === 'string' ? result.data : JSON.stringify(result.data, null, 2)
            }
          ]
        };

      } catch (error) {
        this.logger?.error(`Tool execution error for ${name}:`, error);
        return {
          content: [
            {
              type: 'text',
              text: `Tool execution error: ${(error as Error).message}`
            }
          ],
          isError: true
        };
      }
    });

    // Resources handlers
    this.server.setRequestHandler(ListResourcesRequest, async () => {
      return {
        resources: Array.from(this.resources.values())
      };
    });

    this.server.setRequestHandler(ReadResourceRequest, async (request) => {
      const { uri } = request.params;
      const resource = this.resources.get(uri);
      
      if (!resource) {
        throw new Error(`Resource not found: ${uri}`);
      }

      // For now, return a simple text response
      // In a real implementation, you'd read the actual resource content
      return {
        contents: [
          {
            uri,
            mimeType: 'text/plain',
            text: `Content of resource: ${resource.name || uri}`
          }
        ]
      };
    });

    // Prompts handlers
    this.server.setRequestHandler(ListPromptsRequest, async () => {
      return {
        prompts: Array.from(this.prompts.values())
      };
    });

    this.server.setRequestHandler(GetPromptRequest, async (request) => {
      const { name, arguments: args } = request.params;
      const prompt = this.prompts.get(name);
      
      if (!prompt) {
        throw new Error(`Prompt not found: ${name}`);
      }

      // Return the prompt with any arguments applied
      return {
        description: prompt.description,
        messages: [
          {
            role: 'user',
            content: {
              type: 'text',
              text: `Prompt: ${name}${args ? ` with args: ${JSON.stringify(args)}` : ''}`
            }
          }
        ]
      };
    });
  }

  /**
   * Convert Arif AI tool to MCP tool format
   */
  private convertToMCPTool(tool: Tool): MCPTool {
    return {
      name: tool.id,
      description: tool.description,
      inputSchema: {
        type: 'object',
        properties: tool.parameters || {},
        required: tool.required || []
      }
    };
  }

  /**
   * Add a resource to the server
   */
  public addResource(resource: MCPResource): void {
    this.resources.set(resource.uri, resource);
    this.logger?.debug(`Added MCP resource: ${resource.uri}`);
  }

  /**
   * Add a prompt to the server
   */
  public addPrompt(prompt: MCPPrompt): void {
    this.prompts.set(prompt.name, prompt);
    this.logger?.debug(`Added MCP prompt: ${prompt.name}`);
  }

  /**
   * Remove a resource from the server
   */
  public removeResource(uri: string): void {
    this.resources.delete(uri);
    this.logger?.debug(`Removed MCP resource: ${uri}`);
  }

  /**
   * Remove a prompt from the server
   */
  public removePrompt(name: string): void {
    this.prompts.delete(name);
    this.logger?.debug(`Removed MCP prompt: ${name}`);
  }

  /**
   * Start the server with stdio transport
   */
  public async startStdio(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    this.logger?.info('MCP server started with stdio transport');
  }

  /**
   * Start the server with SSE transport
   */
  public async startSSE(port: number = 3000): Promise<void> {
    const transport = new SSEServerTransport('/sse', port);
    await this.server.connect(transport);
    this.logger?.info(`MCP server started with SSE transport on port ${port}`);
  }

  /**
   * Stop the server
   */
  public async stop(): Promise<void> {
    await this.server.close();
    this.logger?.info('MCP server stopped');
  }

  /**
   * Get server statistics
   */
  public getStats() {
    return {
      tools: this.toolManager.getAllTools().length,
      resources: this.resources.size,
      prompts: this.prompts.size,
      categories: this.toolManager.getCategories()
    };
  }

  /**
   * Setup default resources and prompts
   */
  public setupDefaults(): void {
    // Add default resources
    this.addResource({
      uri: 'arif://system/info',
      name: 'System Information',
      description: 'Information about the Arif AI system',
      mimeType: 'application/json'
    });

    this.addResource({
      uri: 'arif://tools/list',
      name: 'Available Tools',
      description: 'List of all available tools in Arif AI',
      mimeType: 'application/json'
    });

    // Add default prompts
    this.addPrompt({
      name: 'code-review',
      description: 'Perform a code review on the provided code',
      arguments: [
        {
          name: 'code',
          description: 'The code to review',
          required: true
        },
        {
          name: 'language',
          description: 'Programming language of the code',
          required: false
        }
      ]
    });

    this.addPrompt({
      name: 'explain-code',
      description: 'Explain how the provided code works',
      arguments: [
        {
          name: 'code',
          description: 'The code to explain',
          required: true
        },
        {
          name: 'level',
          description: 'Explanation level (beginner, intermediate, advanced)',
          required: false
        }
      ]
    });

    this.addPrompt({
      name: 'generate-tests',
      description: 'Generate unit tests for the provided code',
      arguments: [
        {
          name: 'code',
          description: 'The code to generate tests for',
          required: true
        },
        {
          name: 'framework',
          description: 'Testing framework to use',
          required: false
        }
      ]
    });

    this.logger?.debug('Default MCP resources and prompts added');
  }
}
