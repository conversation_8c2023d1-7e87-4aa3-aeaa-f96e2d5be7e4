/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { EventEmitter } from 'events';
import type { Logger } from '@inkbytefo/s647-shared';
import { AutonomousAgent, type AgentState, type AgentExecutionContext } from './AutonomousAgent.js';
import { AIManager } from '../ai/manager.js';
import { ToolOrchestrator } from '../tools/orchestration/ToolOrchestrator.js';
import { MCPClient } from '../mcp/client/MCPClient.js';

/**
 * Agent configuration
 */
export interface AgentConfig {
  id: string;
  name: string;
  description?: string;
  systemPrompt?: string;
  maxIterations?: number;
  maxPlanningTime?: number;
  capabilities?: string[];
  limitations?: string[];
  enabled?: boolean;
}

/**
 * Agent execution request
 */
export interface AgentExecutionRequest {
  agentId: string;
  goal: string;
  context?: Partial<AgentExecutionContext>;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  timeout?: number;
}

/**
 * Agent execution result
 */
export interface AgentExecutionResult {
  requestId: string;
  agentId: string;
  success: boolean;
  result?: any;
  error?: string;
  duration: number;
  startTime: Date;
  endTime: Date;
}

/**
 * Agent Manager for coordinating multiple autonomous agents
 */
export class AgentManager extends EventEmitter {
  private agents: Map<string, AutonomousAgent> = new Map();
  private agentConfigs: Map<string, AgentConfig> = new Map();
  private activeExecutions: Map<string, Promise<AgentExecutionResult>> = new Map();
  private executionHistory: AgentExecutionResult[] = [];
  private aiManager: AIManager;
  private toolOrchestrator: ToolOrchestrator;
  private mcpClient?: MCPClient;
  private logger?: Logger;

  constructor(
    aiManager: AIManager,
    toolOrchestrator: ToolOrchestrator,
    options: {
      mcpClient?: MCPClient;
      logger?: Logger;
    } = {}
  ) {
    super();
    this.aiManager = aiManager;
    this.toolOrchestrator = toolOrchestrator;
    this.mcpClient = options.mcpClient;
    this.logger = options.logger;
  }

  /**
   * Register an agent
   */
  public registerAgent(config: AgentConfig): void {
    if (this.agents.has(config.id)) {
      throw new Error(`Agent already registered: ${config.id}`);
    }

    const agent = new AutonomousAgent(
      config.id,
      config.name,
      this.aiManager,
      this.toolOrchestrator,
      {
        systemPrompt: config.systemPrompt,
        maxIterations: config.maxIterations,
        maxPlanningTime: config.maxPlanningTime,
        mcpClient: this.mcpClient,
        logger: this.logger
      }
    );

    // Forward agent events
    agent.on('goalStarted', (goal) => this.emit('agentGoalStarted', config.id, goal));
    agent.on('goalCompleted', (goal, result) => this.emit('agentGoalCompleted', config.id, goal, result));
    agent.on('goalFailed', (goal, error) => this.emit('agentGoalFailed', config.id, goal, error));
    agent.on('stepStarted', (step) => this.emit('agentStepStarted', config.id, step));
    agent.on('stepCompleted', (step, result) => this.emit('agentStepCompleted', config.id, step, result));
    agent.on('stepFailed', (step, error) => this.emit('agentStepFailed', config.id, step, error));
    agent.on('humanInteractionRequired', (step, context) => 
      this.emit('agentHumanInteractionRequired', config.id, step, context));

    this.agents.set(config.id, agent);
    this.agentConfigs.set(config.id, config);

    this.logger?.info(`Registered agent: ${config.id} (${config.name})`);
    this.emit('agentRegistered', config.id, config);
  }

  /**
   * Unregister an agent
   */
  public unregisterAgent(agentId: string): boolean {
    const agent = this.agents.get(agentId);
    if (!agent) {
      return false;
    }

    // Stop agent if running
    agent.stop();

    this.agents.delete(agentId);
    this.agentConfigs.delete(agentId);

    this.logger?.info(`Unregistered agent: ${agentId}`);
    this.emit('agentUnregistered', agentId);
    return true;
  }

  /**
   * Execute a goal with a specific agent
   */
  public async executeGoal(request: AgentExecutionRequest): Promise<AgentExecutionResult> {
    const agent = this.agents.get(request.agentId);
    if (!agent) {
      throw new Error(`Agent not found: ${request.agentId}`);
    }

    const config = this.agentConfigs.get(request.agentId);
    if (config && !config.enabled) {
      throw new Error(`Agent is disabled: ${request.agentId}`);
    }

    const requestId = `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Check if agent is already executing
    if (this.isAgentBusy(request.agentId)) {
      throw new Error(`Agent is busy: ${request.agentId}`);
    }

    const executionPromise = this._executeGoal(requestId, request);
    this.activeExecutions.set(requestId, executionPromise);

    try {
      const result = await executionPromise;
      this.executionHistory.push(result);
      return result;
    } finally {
      this.activeExecutions.delete(requestId);
    }
  }

  /**
   * Internal goal execution
   */
  private async _executeGoal(
    requestId: string,
    request: AgentExecutionRequest
  ): Promise<AgentExecutionResult> {
    const startTime = new Date();
    const agent = this.agents.get(request.agentId)!;

    this.emit('executionStarted', requestId, request);

    try {
      const result = await agent.executeGoal(request.goal, request.context);
      
      const executionResult: AgentExecutionResult = {
        requestId,
        agentId: request.agentId,
        success: result.success,
        result: result.result,
        error: result.error,
        duration: Date.now() - startTime.getTime(),
        startTime,
        endTime: new Date()
      };

      this.emit('executionCompleted', requestId, executionResult);
      return executionResult;

    } catch (error) {
      const executionResult: AgentExecutionResult = {
        requestId,
        agentId: request.agentId,
        success: false,
        error: (error as Error).message,
        duration: Date.now() - startTime.getTime(),
        startTime,
        endTime: new Date()
      };

      this.emit('executionFailed', requestId, executionResult);
      return executionResult;
    }
  }

  /**
   * Execute goal with best available agent
   */
  public async executeGoalWithBestAgent(
    goal: string,
    context?: Partial<AgentExecutionContext>,
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<AgentExecutionResult> {
    const availableAgents = this.getAvailableAgents();
    if (availableAgents.length === 0) {
      throw new Error('No available agents');
    }

    // Simple agent selection - in production, use more sophisticated selection
    const selectedAgent = this.selectBestAgent(availableAgents, goal, priority);

    return await this.executeGoal({
      agentId: selectedAgent.id,
      goal,
      context,
      priority
    });
  }

  /**
   * Select best agent for a goal
   */
  private selectBestAgent(
    availableAgents: AgentConfig[],
    goal: string,
    priority: string
  ): AgentConfig {
    // Simple selection based on performance and availability
    // In production, this could use ML models or more sophisticated heuristics
    
    const agentScores = availableAgents.map(config => {
      const agent = this.agents.get(config.id)!;
      const state = agent.getState();
      
      let score = state.performance.successRate * 100;
      
      // Prefer agents with relevant capabilities
      if (goal.toLowerCase().includes('code') && config.capabilities?.includes('code_analysis')) {
        score += 20;
      }
      if (goal.toLowerCase().includes('file') && config.capabilities?.includes('file_operations')) {
        score += 20;
      }
      
      // Adjust for priority
      if (priority === 'critical' && config.capabilities?.includes('high_reliability')) {
        score += 30;
      }
      
      return { config, score };
    });

    agentScores.sort((a, b) => b.score - a.score);
    return agentScores[0].config;
  }

  /**
   * Get available agents
   */
  public getAvailableAgents(): AgentConfig[] {
    return Array.from(this.agentConfigs.values())
      .filter(config => config.enabled !== false && !this.isAgentBusy(config.id));
  }

  /**
   * Check if agent is busy
   */
  public isAgentBusy(agentId: string): boolean {
    const agent = this.agents.get(agentId);
    if (!agent) return false;

    const state = agent.getState();
    return state.status !== 'idle';
  }

  /**
   * Get agent state
   */
  public getAgentState(agentId: string): AgentState | undefined {
    const agent = this.agents.get(agentId);
    return agent?.getState();
  }

  /**
   * Get all agent states
   */
  public getAllAgentStates(): Map<string, AgentState> {
    const states = new Map<string, AgentState>();
    for (const [id, agent] of this.agents) {
      states.set(id, agent.getState());
    }
    return states;
  }

  /**
   * Pause agent
   */
  public pauseAgent(agentId: string): boolean {
    const agent = this.agents.get(agentId);
    if (!agent) return false;

    agent.pause();
    return true;
  }

  /**
   * Resume agent
   */
  public resumeAgent(agentId: string): boolean {
    const agent = this.agents.get(agentId);
    if (!agent) return false;

    agent.resume();
    return true;
  }

  /**
   * Stop agent
   */
  public stopAgent(agentId: string): boolean {
    const agent = this.agents.get(agentId);
    if (!agent) return false;

    agent.stop();
    return true;
  }

  /**
   * Stop all agents
   */
  public stopAllAgents(): void {
    for (const agent of this.agents.values()) {
      agent.stop();
    }
    this.logger?.info('Stopped all agents');
  }

  /**
   * Get execution history
   */
  public getExecutionHistory(limit?: number): AgentExecutionResult[] {
    const history = [...this.executionHistory].reverse(); // Most recent first
    return limit ? history.slice(0, limit) : history;
  }

  /**
   * Get agent performance metrics
   */
  public getAgentMetrics(agentId: string): any {
    const agent = this.agents.get(agentId);
    if (!agent) return null;

    const state = agent.getState();
    const agentHistory = this.executionHistory.filter(r => r.agentId === agentId);

    return {
      ...state.performance,
      totalExecutions: agentHistory.length,
      recentExecutions: agentHistory.slice(-10),
      averageSuccessRate: agentHistory.length > 0 
        ? agentHistory.filter(r => r.success).length / agentHistory.length 
        : 0
    };
  }

  /**
   * Get system metrics
   */
  public getSystemMetrics(): any {
    const totalAgents = this.agents.size;
    const activeAgents = Array.from(this.agents.values())
      .filter(agent => agent.getState().status !== 'idle').length;
    
    const totalExecutions = this.executionHistory.length;
    const successfulExecutions = this.executionHistory.filter(r => r.success).length;
    
    return {
      totalAgents,
      activeAgents,
      totalExecutions,
      successfulExecutions,
      overallSuccessRate: totalExecutions > 0 ? successfulExecutions / totalExecutions : 0,
      averageExecutionTime: totalExecutions > 0 
        ? this.executionHistory.reduce((sum, r) => sum + r.duration, 0) / totalExecutions 
        : 0
    };
  }

  /**
   * Register default agents
   */
  public registerDefaultAgents(): void {
    // General purpose agent
    this.registerAgent({
      id: 'general',
      name: 'General Assistant',
      description: 'General purpose autonomous agent for various tasks',
      capabilities: ['general_assistance', 'tool_execution', 'workflow_orchestration'],
      enabled: true
    });

    // Code specialist agent
    this.registerAgent({
      id: 'code-specialist',
      name: 'Code Specialist',
      description: 'Specialized agent for code analysis, generation, and debugging',
      systemPrompt: 'You are a code specialist agent. Focus on code-related tasks with high accuracy.',
      capabilities: ['code_analysis', 'code_generation', 'debugging', 'testing'],
      enabled: true
    });

    // File operations agent
    this.registerAgent({
      id: 'file-ops',
      name: 'File Operations Agent',
      description: 'Specialized agent for file and directory operations',
      capabilities: ['file_operations', 'directory_management', 'search'],
      enabled: true
    });

    this.logger?.info('Registered default agents');
  }
}
