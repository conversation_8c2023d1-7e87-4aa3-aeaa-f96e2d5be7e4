/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { EventEmitter } from 'events';
import type { Logger, ChatMessage, ChatCompletionRequest } from '@inkbytefo/s647-shared';
import { AIManager } from '../ai/manager.js';
import { ToolOrchestrator, type ToolExecutionPlan } from '../tools/orchestration/ToolOrchestrator.js';
import { MCPClient, MCPAgent, type MCPAgentConfig } from '../mcp/index.js';

/**
 * Agent planning state
 */
export interface AgentPlan {
  id: string;
  goal: string;
  steps: AgentPlanStep[];
  context: Record<string, any>;
  estimatedDuration?: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Agent plan step
 */
export interface AgentPlanStep {
  id: string;
  description: string;
  type: 'think' | 'tool' | 'mcp' | 'workflow' | 'human';
  toolId?: string;
  workflowId?: string;
  mcpAction?: string;
  params?: any;
  expectedOutput?: string;
  dependencies?: string[];
  status: 'pending' | 'executing' | 'completed' | 'failed' | 'skipped';
  result?: any;
  error?: string;
}

/**
 * Agent execution context
 */
export interface AgentExecutionContext {
  sessionId: string;
  userId: string;
  goal: string;
  constraints: string[];
  preferences: Record<string, any>;
  memory: AgentMemory[];
  environment: Record<string, any>;
}

/**
 * Agent memory item
 */
export interface AgentMemory {
  id: string;
  type: 'observation' | 'action' | 'result' | 'reflection';
  content: string;
  timestamp: Date;
  importance: number; // 1-10 scale
  tags: string[];
  metadata?: Record<string, any>;
}

/**
 * Agent state
 */
export interface AgentState {
  id: string;
  status: 'idle' | 'planning' | 'executing' | 'monitoring' | 'reflecting' | 'error' | 'paused';
  currentPlan?: AgentPlan;
  currentStep?: string;
  executionContext?: AgentExecutionContext;
  performance: {
    tasksCompleted: number;
    successRate: number;
    averageExecutionTime: number;
    lastActivity: Date;
  };
  capabilities: string[];
  limitations: string[];
}

/**
 * Autonomous Agent implementing Anthropic's agent patterns
 * Self-directing agent with planning, execution, and monitoring capabilities
 */
export class AutonomousAgent extends EventEmitter {
  private id: string;
  private name: string;
  private aiManager: AIManager;
  private toolOrchestrator: ToolOrchestrator;
  private mcpClient?: MCPClient;
  private mcpAgent?: MCPAgent;
  private logger?: Logger;
  private state: AgentState;
  private systemPrompt: string;
  private maxIterations: number;
  private maxPlanningTime: number;
  private isRunning: boolean = false;

  constructor(
    id: string,
    name: string,
    aiManager: AIManager,
    toolOrchestrator: ToolOrchestrator,
    options: {
      systemPrompt?: string;
      maxIterations?: number;
      maxPlanningTime?: number;
      mcpClient?: MCPClient;
      logger?: Logger;
    } = {}
  ) {
    super();
    this.id = id;
    this.name = name;
    this.aiManager = aiManager;
    this.toolOrchestrator = toolOrchestrator;
    this.mcpClient = options.mcpClient;
    this.logger = options.logger;
    this.maxIterations = options.maxIterations || 50;
    this.maxPlanningTime = options.maxPlanningTime || 300000; // 5 minutes

    this.systemPrompt = options.systemPrompt || this.getDefaultSystemPrompt();

    this.state = {
      id,
      status: 'idle',
      performance: {
        tasksCompleted: 0,
        successRate: 0,
        averageExecutionTime: 0,
        lastActivity: new Date()
      },
      capabilities: this.getCapabilities(),
      limitations: this.getLimitations()
    };

    // Setup MCP agent if client is available
    if (this.mcpClient) {
      this.setupMCPAgent();
    }
  }

  /**
   * Execute a goal autonomously
   */
  public async executeGoal(
    goal: string,
    context: Partial<AgentExecutionContext> = {}
  ): Promise<{ success: boolean; result?: any; error?: string }> {
    if (this.isRunning) {
      throw new Error('Agent is already running');
    }

    this.isRunning = true;
    this.state.status = 'planning';
    this.state.executionContext = {
      sessionId: context.sessionId || `session-${Date.now()}`,
      userId: context.userId || 'anonymous',
      goal,
      constraints: context.constraints || [],
      preferences: context.preferences || {},
      memory: context.memory || [],
      environment: context.environment || {}
    };

    const startTime = Date.now();

    try {
      this.emit('goalStarted', goal);
      this.logger?.info(`Agent ${this.name} starting goal: ${goal}`);

      // Phase 1: Planning
      const plan = await this.createPlan(goal, this.state.executionContext);
      this.state.currentPlan = plan;

      // Phase 2: Execution
      const result = await this.executePlan(plan);

      // Phase 3: Reflection
      await this.reflect(plan, result);

      // Update performance metrics
      this.updatePerformanceMetrics(true, Date.now() - startTime);

      this.emit('goalCompleted', goal, result);
      this.logger?.info(`Agent ${this.name} completed goal: ${goal}`);

      return { success: true, result };

    } catch (error) {
      this.updatePerformanceMetrics(false, Date.now() - startTime);
      this.state.status = 'error';

      this.emit('goalFailed', goal, error);
      this.logger?.error(`Agent ${this.name} failed goal: ${goal}`, error);

      return { success: false, error: (error as Error).message };

    } finally {
      this.isRunning = false;
      this.state.status = 'idle';
      this.state.currentPlan = undefined;
      this.state.currentStep = undefined;
    }
  }

  /**
   * Create execution plan for the goal
   */
  private async createPlan(goal: string, context: AgentExecutionContext): Promise<AgentPlan> {
    this.logger?.debug(`Creating plan for goal: ${goal}`);

    const planningMessages: ChatMessage[] = [
      {
        role: 'system',
        content: this.systemPrompt
      },
      {
        role: 'user',
        content: this.buildPlanningPrompt(goal, context)
      }
    ];

    const planningRequest: ChatCompletionRequest = {
      messages: planningMessages,
      temperature: 0.7,
      maxTokens: 2000
    };

    const response = await this.aiManager.createChatCompletion(planningRequest);
    if (!response.success) {
      throw new Error(`Planning failed: ${response.error?.message}`);
    }

    const planText = response.data?.choices[0]?.message?.content || '';
    const plan = this.parsePlan(planText, goal);

    this.addMemory({
      type: 'action',
      content: `Created plan for goal: ${goal}`,
      importance: 8,
      tags: ['planning', 'goal'],
      metadata: { planId: plan.id, stepCount: plan.steps.length }
    });

    return plan;
  }

  /**
   * Execute the plan
   */
  private async executePlan(plan: AgentPlan): Promise<any> {
    this.state.status = 'executing';
    this.logger?.debug(`Executing plan: ${plan.id}`);

    let iteration = 0;
    const results: any[] = [];

    for (const step of plan.steps) {
      if (iteration >= this.maxIterations) {
        throw new Error('Maximum iterations exceeded');
      }

      this.state.currentStep = step.id;
      step.status = 'executing';

      try {
        this.emit('stepStarted', step);
        const result = await this.executeStep(step, plan.context);
        
        step.status = 'completed';
        step.result = result;
        results.push(result);

        this.addMemory({
          type: 'result',
          content: `Completed step: ${step.description}`,
          importance: 6,
          tags: ['execution', 'step'],
          metadata: { stepId: step.id, result }
        });

        this.emit('stepCompleted', step, result);

      } catch (error) {
        step.status = 'failed';
        step.error = (error as Error).message;

        this.addMemory({
          type: 'observation',
          content: `Step failed: ${step.description} - ${step.error}`,
          importance: 7,
          tags: ['execution', 'error'],
          metadata: { stepId: step.id, error: step.error }
        });

        this.emit('stepFailed', step, error);

        // Decide whether to continue or abort
        const shouldContinue = await this.handleStepFailure(step, plan);
        if (!shouldContinue) {
          throw error;
        }
      }

      iteration++;
    }

    return results;
  }

  /**
   * Execute a single step
   */
  private async executeStep(step: AgentPlanStep, context: Record<string, any>): Promise<any> {
    switch (step.type) {
      case 'think':
        return await this.executeThinkStep(step, context);
      case 'tool':
        return await this.executeToolStep(step, context);
      case 'workflow':
        return await this.executeWorkflowStep(step, context);
      case 'mcp':
        return await this.executeMCPStep(step, context);
      case 'human':
        return await this.executeHumanStep(step, context);
      default:
        throw new Error(`Unknown step type: ${step.type}`);
    }
  }

  /**
   * Execute thinking step
   */
  private async executeThinkStep(step: AgentPlanStep, context: Record<string, any>): Promise<any> {
    const thinkingMessages: ChatMessage[] = [
      {
        role: 'system',
        content: this.systemPrompt
      },
      {
        role: 'user',
        content: `Think about: ${step.description}\n\nContext: ${JSON.stringify(context, null, 2)}`
      }
    ];

    const response = await this.aiManager.createChatCompletion({
      messages: thinkingMessages,
      temperature: 0.8,
      maxTokens: 1000
    });

    if (!response.success) {
      throw new Error(`Thinking failed: ${response.error?.message}`);
    }

    return response.data?.choices[0]?.message?.content || '';
  }

  /**
   * Execute tool step
   */
  private async executeToolStep(step: AgentPlanStep, context: Record<string, any>): Promise<any> {
    if (!step.toolId) {
      throw new Error('Tool ID is required for tool step');
    }

    const toolContext = {
      workingDirectory: process.cwd(),
      environment: process.env,
      user: {
        id: this.state.executionContext?.userId || 'anonymous',
        name: 'Agent User'
      }
    };

    const result = await this.toolOrchestrator.getToolManager().executeTool(
      step.toolId,
      step.params || {},
      toolContext
    );

    if (!result.success) {
      throw new Error(`Tool execution failed: ${result.error?.message}`);
    }

    return result.data;
  }

  /**
   * Execute workflow step
   */
  private async executeWorkflowStep(step: AgentPlanStep, context: Record<string, any>): Promise<any> {
    if (!step.workflowId) {
      throw new Error('Workflow ID is required for workflow step');
    }

    const toolContext = {
      workingDirectory: process.cwd(),
      environment: process.env,
      user: {
        id: this.state.executionContext?.userId || 'anonymous',
        name: 'Agent User'
      }
    };

    const result = await this.toolOrchestrator.executePlan(
      step.workflowId,
      toolContext,
      step.params
    );

    if (!result.success) {
      throw new Error(`Workflow execution failed: ${result.error}`);
    }

    return result;
  }

  /**
   * Execute MCP step
   */
  private async executeMCPStep(step: AgentPlanStep, context: Record<string, any>): Promise<any> {
    if (!this.mcpAgent) {
      throw new Error('MCP agent not available');
    }

    if (!step.mcpAction) {
      throw new Error('MCP action is required for MCP step');
    }

    // This would depend on the specific MCP action
    // For now, return a placeholder
    return { mcpAction: step.mcpAction, params: step.params };
  }

  /**
   * Execute human interaction step
   */
  private async executeHumanStep(step: AgentPlanStep, context: Record<string, any>): Promise<any> {
    // Emit event for human interaction
    this.emit('humanInteractionRequired', step, context);
    
    // In a real implementation, this would wait for human input
    // For now, return a placeholder
    return { humanInteractionRequired: true, step: step.description };
  }

  /**
   * Handle step failure
   */
  private async handleStepFailure(step: AgentPlanStep, plan: AgentPlan): Promise<boolean> {
    // Simple strategy: continue if step is not critical
    // In a more sophisticated implementation, this could involve replanning
    return step.type !== 'tool' || plan.priority !== 'critical';
  }

  /**
   * Reflect on execution results
   */
  private async reflect(plan: AgentPlan, results: any[]): Promise<void> {
    this.state.status = 'reflecting';
    this.logger?.debug(`Reflecting on plan execution: ${plan.id}`);

    const reflectionPrompt = this.buildReflectionPrompt(plan, results);
    const reflectionMessages: ChatMessage[] = [
      {
        role: 'system',
        content: this.systemPrompt
      },
      {
        role: 'user',
        content: reflectionPrompt
      }
    ];

    try {
      const response = await this.aiManager.createChatCompletion({
        messages: reflectionMessages,
        temperature: 0.6,
        maxTokens: 1000
      });

      if (response.success) {
        const reflection = response.data?.choices[0]?.message?.content || '';
        this.addMemory({
          type: 'reflection',
          content: reflection,
          importance: 9,
          tags: ['reflection', 'learning'],
          metadata: { planId: plan.id }
        });
      }
    } catch (error) {
      this.logger?.warn('Reflection failed:', error);
    }
  }

  /**
   * Add memory item
   */
  private addMemory(memory: Omit<AgentMemory, 'id' | 'timestamp'>): void {
    if (!this.state.executionContext) return;

    const memoryItem: AgentMemory = {
      id: `memory-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      ...memory
    };

    this.state.executionContext.memory.push(memoryItem);

    // Limit memory size
    const maxMemorySize = 1000;
    if (this.state.executionContext.memory.length > maxMemorySize) {
      // Remove least important memories
      this.state.executionContext.memory.sort((a, b) => b.importance - a.importance);
      this.state.executionContext.memory = this.state.executionContext.memory.slice(0, maxMemorySize);
    }
  }

  /**
   * Update performance metrics
   */
  private updatePerformanceMetrics(success: boolean, duration: number): void {
    this.state.performance.tasksCompleted++;
    this.state.performance.lastActivity = new Date();

    // Update success rate
    const totalTasks = this.state.performance.tasksCompleted;
    const currentSuccessRate = this.state.performance.successRate;
    const newSuccessCount = Math.round(currentSuccessRate * (totalTasks - 1)) + (success ? 1 : 0);
    this.state.performance.successRate = newSuccessCount / totalTasks;

    // Update average execution time
    const currentAverage = this.state.performance.averageExecutionTime;
    this.state.performance.averageExecutionTime = 
      ((currentAverage * (totalTasks - 1)) + duration) / totalTasks;
  }

  /**
   * Get agent capabilities
   */
  private getCapabilities(): string[] {
    return [
      'autonomous_planning',
      'tool_execution',
      'workflow_orchestration',
      'self_reflection',
      'memory_management',
      'error_recovery',
      'human_interaction'
    ];
  }

  /**
   * Get agent limitations
   */
  private getLimitations(): string[] {
    return [
      'no_internet_access_by_default',
      'limited_context_window',
      'no_persistent_storage',
      'requires_human_oversight',
      'limited_reasoning_depth'
    ];
  }

  /**
   * Setup MCP agent
   */
  private setupMCPAgent(): void {
    if (!this.mcpClient) return;

    const mcpConfig: MCPAgentConfig = {
      name: `${this.name}-mcp`,
      description: `MCP agent for ${this.name}`,
      workflows: [],
      tools: [],
      resources: [],
      prompts: [],
      maxIterations: this.maxIterations,
      enableMemory: true
    };

    this.mcpAgent = new MCPAgent(mcpConfig, this.mcpClient, this.logger);
  }

  // Helper methods for prompt building would be implemented here
  private getDefaultSystemPrompt(): string {
    return `You are an autonomous AI agent named ${this.name}. Your role is to:
1. Analyze goals and create detailed execution plans
2. Execute plans step by step using available tools
3. Monitor progress and adapt as needed
4. Reflect on results to improve future performance

You have access to various tools and can orchestrate complex workflows.
Always think step by step and explain your reasoning.
If you encounter errors, try to recover gracefully.
Ask for human help when needed.`;
  }

  private buildPlanningPrompt(goal: string, context: AgentExecutionContext): string {
    return `Create a detailed execution plan for this goal: "${goal}"

Context:
- User ID: ${context.userId}
- Constraints: ${context.constraints.join(', ') || 'None'}
- Environment: ${JSON.stringify(context.environment, null, 2)}

Available capabilities: ${this.state.capabilities.join(', ')}

Please create a step-by-step plan with the following format:
1. Step description
2. Step type (think/tool/workflow/mcp/human)
3. Required parameters
4. Expected output

Be specific and actionable.`;
  }

  private buildReflectionPrompt(plan: AgentPlan, results: any[]): string {
    return `Reflect on the execution of plan "${plan.id}" for goal: "${plan.goal}"

Plan steps: ${plan.steps.length}
Results: ${results.length}

What went well? What could be improved? What did you learn?
How would you approach similar goals in the future?`;
  }

  private parsePlan(planText: string, goal: string): AgentPlan {
    // Simple plan parsing - in production, use more sophisticated parsing
    const steps: AgentPlanStep[] = [];
    const lines = planText.split('\n').filter(line => line.trim());

    let stepId = 1;
    for (const line of lines) {
      if (line.match(/^\d+\./)) {
        steps.push({
          id: `step-${stepId++}`,
          description: line.replace(/^\d+\.\s*/, ''),
          type: 'think', // Default type
          status: 'pending'
        });
      }
    }

    return {
      id: `plan-${Date.now()}`,
      goal,
      steps,
      context: {},
      priority: 'medium'
    };
  }

  /**
   * Get current state
   */
  public getState(): AgentState {
    return { ...this.state };
  }

  /**
   * Pause agent execution
   */
  public pause(): void {
    this.state.status = 'paused';
    this.emit('agentPaused');
  }

  /**
   * Resume agent execution
   */
  public resume(): void {
    if (this.state.status === 'paused') {
      this.state.status = 'executing';
      this.emit('agentResumed');
    }
  }

  /**
   * Stop agent execution
   */
  public stop(): void {
    this.isRunning = false;
    this.state.status = 'idle';
    this.emit('agentStopped');
  }
}
