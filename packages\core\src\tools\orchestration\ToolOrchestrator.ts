/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { EventEmitter } from 'events';
import type {
  Tool,
  ToolId,
  ToolParams,
  ToolContext,
  ToolResult,
  AsyncResult,
  Logger
} from '@inkbytefo/s647-shared';
import { ToolManager } from '../manager/ToolManager.js';

/**
 * Tool execution plan step
 */
export interface ToolExecutionStep {
  id: string;
  toolId: ToolId;
  params: ToolParams;
  dependsOn?: string[];
  condition?: string;
  retries?: number;
  timeout?: number;
  onSuccess?: string;
  onError?: string;
}

/**
 * Tool execution plan
 */
export interface ToolExecutionPlan {
  id: string;
  name: string;
  description?: string;
  steps: ToolExecutionStep[];
  variables?: Record<string, any>;
  timeout?: number;
  maxRetries?: number;
}

/**
 * Tool execution result
 */
export interface ToolExecutionResult {
  stepId: string;
  toolId: ToolId;
  success: boolean;
  result?: any;
  error?: string;
  duration: number;
  retries: number;
}

/**
 * Plan execution result
 */
export interface PlanExecutionResult {
  planId: string;
  success: boolean;
  steps: ToolExecutionResult[];
  variables: Record<string, any>;
  totalDuration: number;
  error?: string;
}

/**
 * Tool orchestrator for managing complex tool execution workflows
 * Implements Anthropic's workflow patterns for tool coordination
 */
export class ToolOrchestrator extends EventEmitter {
  private toolManager: ToolManager;
  private logger?: Logger;
  private executionPlans: Map<string, ToolExecutionPlan> = new Map();
  private activeExecutions: Map<string, Promise<PlanExecutionResult>> = new Map();

  constructor(toolManager: ToolManager, logger?: Logger) {
    super();
    this.toolManager = toolManager;
    this.logger = logger;
  }

  /**
   * Register an execution plan
   */
  public registerPlan(plan: ToolExecutionPlan): void {
    const validation = this.validatePlan(plan);
    if (!validation.valid) {
      throw new Error(`Invalid execution plan: ${validation.errors.join(', ')}`);
    }

    this.executionPlans.set(plan.id, plan);
    this.logger?.debug(`Registered execution plan: ${plan.id}`);
  }

  /**
   * Execute a plan
   */
  public async executePlan(
    planId: string,
    context: ToolContext,
    initialVariables?: Record<string, any>
  ): Promise<PlanExecutionResult> {
    const plan = this.executionPlans.get(planId);
    if (!plan) {
      throw new Error(`Execution plan not found: ${planId}`);
    }

    // Check if already executing
    if (this.activeExecutions.has(planId)) {
      this.logger?.warn(`Plan ${planId} is already executing, waiting for completion`);
      return await this.activeExecutions.get(planId)!;
    }

    const executionPromise = this._executePlan(plan, context, initialVariables);
    this.activeExecutions.set(planId, executionPromise);

    try {
      const result = await executionPromise;
      return result;
    } finally {
      this.activeExecutions.delete(planId);
    }
  }

  /**
   * Internal plan execution
   */
  private async _executePlan(
    plan: ToolExecutionPlan,
    context: ToolContext,
    initialVariables?: Record<string, any>
  ): Promise<PlanExecutionResult> {
    const startTime = Date.now();
    const variables = { ...plan.variables, ...initialVariables };
    const stepResults: ToolExecutionResult[] = [];
    const completedSteps = new Set<string>();

    this.emit('planStarted', plan.id);
    this.logger?.info(`Starting execution plan: ${plan.id}`);

    try {
      // Execute steps based on dependencies
      while (completedSteps.size < plan.steps.length) {
        const readySteps = plan.steps.filter(step => 
          !completedSteps.has(step.id) &&
          this.areDependenciesMet(step, completedSteps)
        );

        if (readySteps.length === 0) {
          throw new Error('Circular dependency detected or no steps ready to execute');
        }

        // Execute ready steps in parallel
        const stepPromises = readySteps.map(step => 
          this.executeStep(step, context, variables, stepResults)
        );

        const stepResults_batch = await Promise.allSettled(stepPromises);
        
        for (let i = 0; i < stepResults_batch.length; i++) {
          const stepResult = stepResults_batch[i];
          const step = readySteps[i];

          if (stepResult.status === 'fulfilled') {
            stepResults.push(stepResult.value);
            completedSteps.add(step.id);
            
            // Update variables with step result
            if (stepResult.value.success && stepResult.value.result !== undefined) {
              variables[step.id] = stepResult.value.result;
            }
          } else {
            const errorResult: ToolExecutionResult = {
              stepId: step.id,
              toolId: step.toolId,
              success: false,
              error: stepResult.reason?.message || 'Unknown error',
              duration: 0,
              retries: 0
            };
            stepResults.push(errorResult);
            
            // Handle error based on step configuration
            if (!step.onError) {
              throw new Error(`Step ${step.id} failed: ${errorResult.error}`);
            }
          }
        }
      }

      const result: PlanExecutionResult = {
        planId: plan.id,
        success: true,
        steps: stepResults,
        variables,
        totalDuration: Date.now() - startTime
      };

      this.emit('planCompleted', plan.id, result);
      this.logger?.info(`Execution plan completed: ${plan.id}`);
      return result;

    } catch (error) {
      const result: PlanExecutionResult = {
        planId: plan.id,
        success: false,
        steps: stepResults,
        variables,
        totalDuration: Date.now() - startTime,
        error: (error as Error).message
      };

      this.emit('planFailed', plan.id, result);
      this.logger?.error(`Execution plan failed: ${plan.id}`, error);
      return result;
    }
  }

  /**
   * Execute a single step
   */
  private async executeStep(
    step: ToolExecutionStep,
    context: ToolContext,
    variables: Record<string, any>,
    previousResults: ToolExecutionResult[]
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    let retries = 0;
    const maxRetries = step.retries || 0;

    this.emit('stepStarted', step.id, step.toolId);

    while (retries <= maxRetries) {
      try {
        // Check condition if specified
        if (step.condition && !this.evaluateCondition(step.condition, variables)) {
          return {
            stepId: step.id,
            toolId: step.toolId,
            success: true,
            result: 'skipped',
            duration: Date.now() - startTime,
            retries
          };
        }

        // Resolve parameters with variables
        const resolvedParams = this.resolveParameters(step.params, variables);

        // Execute tool with timeout
        const timeoutMs = step.timeout || 30000;
        const toolResult = await this.executeWithTimeout(
          () => this.toolManager.executeTool(step.toolId, resolvedParams, context),
          timeoutMs
        );

        if (!toolResult.success) {
          throw new Error(toolResult.error?.message || 'Tool execution failed');
        }

        const result: ToolExecutionResult = {
          stepId: step.id,
          toolId: step.toolId,
          success: true,
          result: toolResult.data,
          duration: Date.now() - startTime,
          retries
        };

        this.emit('stepCompleted', step.id, result);
        return result;

      } catch (error) {
        retries++;
        this.logger?.warn(`Step ${step.id} failed (attempt ${retries}/${maxRetries + 1}):`, error);

        if (retries > maxRetries) {
          const result: ToolExecutionResult = {
            stepId: step.id,
            toolId: step.toolId,
            success: false,
            error: (error as Error).message,
            duration: Date.now() - startTime,
            retries
          };

          this.emit('stepFailed', step.id, result);
          return result;
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
      }
    }

    throw new Error('Unexpected end of retry loop');
  }

  /**
   * Check if step dependencies are met
   */
  private areDependenciesMet(step: ToolExecutionStep, completedSteps: Set<string>): boolean {
    if (!step.dependsOn || step.dependsOn.length === 0) {
      return true;
    }

    return step.dependsOn.every(dep => completedSteps.has(dep));
  }

  /**
   * Resolve parameters with variables
   */
  private resolveParameters(params: ToolParams, variables: Record<string, any>): ToolParams {
    if (typeof params === 'string') {
      return this.resolveString(params, variables);
    }

    if (Array.isArray(params)) {
      return params.map(item => this.resolveParameters(item, variables));
    }

    if (typeof params === 'object' && params !== null) {
      const resolved: any = {};
      for (const [key, value] of Object.entries(params)) {
        resolved[key] = this.resolveParameters(value, variables);
      }
      return resolved;
    }

    return params;
  }

  /**
   * Resolve string with variables
   */
  private resolveString(str: string, variables: Record<string, any>): string {
    return str.replace(/\$\{([^}]+)\}/g, (match, varName) => {
      return variables[varName] !== undefined ? String(variables[varName]) : match;
    });
  }

  /**
   * Evaluate condition
   */
  private evaluateCondition(condition: string, variables: Record<string, any>): boolean {
    try {
      // Simple condition evaluation - replace with safer evaluator in production
      const resolvedCondition = this.resolveString(condition, variables);
      return new Function('return ' + resolvedCondition)();
    } catch (error) {
      this.logger?.warn(`Failed to evaluate condition: ${condition}`, error);
      return false;
    }
  }

  /**
   * Execute with timeout
   */
  private async executeWithTimeout<T>(
    fn: () => Promise<T>,
    timeoutMs: number
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeoutMs}ms`));
      }, timeoutMs);

      fn()
        .then(result => {
          clearTimeout(timeout);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeout);
          reject(error);
        });
    });
  }

  /**
   * Validate execution plan
   */
  private validatePlan(plan: ToolExecutionPlan): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!plan.id) {
      errors.push('Plan ID is required');
    }

    if (!plan.name) {
      errors.push('Plan name is required');
    }

    if (!plan.steps || plan.steps.length === 0) {
      errors.push('Plan must have at least one step');
    }

    // Validate steps
    const stepIds = new Set<string>();
    for (const step of plan.steps) {
      if (!step.id) {
        errors.push('Step ID is required');
      } else if (stepIds.has(step.id)) {
        errors.push(`Duplicate step ID: ${step.id}`);
      } else {
        stepIds.add(step.id);
      }

      if (!step.toolId) {
        errors.push(`Tool ID is required for step ${step.id}`);
      }

      // Validate dependencies
      if (step.dependsOn) {
        for (const dep of step.dependsOn) {
          if (!stepIds.has(dep) && !plan.steps.some(s => s.id === dep)) {
            errors.push(`Invalid dependency ${dep} for step ${step.id}`);
          }
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get registered plans
   */
  public getPlans(): ToolExecutionPlan[] {
    return Array.from(this.executionPlans.values());
  }

  /**
   * Get plan by ID
   */
  public getPlan(planId: string): ToolExecutionPlan | undefined {
    return this.executionPlans.get(planId);
  }

  /**
   * Remove plan
   */
  public removePlan(planId: string): boolean {
    return this.executionPlans.delete(planId);
  }

  /**
   * Get active executions
   */
  public getActiveExecutions(): string[] {
    return Array.from(this.activeExecutions.keys());
  }

  /**
   * Cancel active execution
   */
  public async cancelExecution(planId: string): Promise<boolean> {
    if (!this.activeExecutions.has(planId)) {
      return false;
    }

    // Note: This is a simple cancellation - in production you'd want more sophisticated cancellation
    this.activeExecutions.delete(planId);
    this.emit('planCancelled', planId);
    this.logger?.info(`Execution cancelled: ${planId}`);
    return true;
  }
}
