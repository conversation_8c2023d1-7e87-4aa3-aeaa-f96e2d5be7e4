/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type { MCPWorkflow, MCPWorkflowStep, MCPAgentConfig } from '../types/index.js';

/**
 * Utility functions for MCP operations
 */

/**
 * Create a simple tool workflow
 */
export function createToolWorkflow(
  id: string,
  name: string,
  toolId: string,
  params?: any,
  description?: string
): MCPWorkflow {
  return {
    id,
    name,
    description,
    steps: [
      {
        id: 'main',
        type: 'tool',
        toolId,
        params
      }
    ]
  };
}

/**
 * Create a sequential workflow
 */
export function createSequentialWorkflow(
  id: string,
  name: string,
  steps: Array<{
    id: string;
    type: 'tool' | 'prompt' | 'resource';
    toolId?: string;
    promptId?: string;
    resourceId?: string;
    params?: any;
  }>,
  description?: string
): MCPWorkflow {
  return {
    id,
    name,
    description,
    steps: steps.map(step => ({
      id: step.id,
      type: step.type,
      toolId: step.toolId,
      promptId: step.promptId,
      resourceId: step.resourceId,
      params: step.params
    }))
  };
}

/**
 * Create a conditional workflow
 */
export function createConditionalWorkflow(
  id: string,
  name: string,
  condition: string,
  trueSteps: MCPWorkflowStep[],
  falseSteps?: MCPWorkflowStep[],
  description?: string
): MCPWorkflow {
  const steps: MCPWorkflowStep[] = [
    {
      id: 'condition',
      type: 'condition',
      condition,
      steps: trueSteps
    }
  ];

  if (falseSteps && falseSteps.length > 0) {
    steps.push({
      id: 'else',
      type: 'condition',
      condition: `!(${condition})`,
      steps: falseSteps
    });
  }

  return {
    id,
    name,
    description,
    steps
  };
}

/**
 * Create a parallel workflow
 */
export function createParallelWorkflow(
  id: string,
  name: string,
  parallelSteps: MCPWorkflowStep[],
  description?: string
): MCPWorkflow {
  return {
    id,
    name,
    description,
    steps: [
      {
        id: 'parallel',
        type: 'parallel',
        steps: parallelSteps
      }
    ]
  };
}

/**
 * Validate workflow configuration
 */
export function validateWorkflow(workflow: MCPWorkflow): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!workflow.id) {
    errors.push('Workflow ID is required');
  }

  if (!workflow.name) {
    errors.push('Workflow name is required');
  }

  if (!workflow.steps || workflow.steps.length === 0) {
    errors.push('Workflow must have at least one step');
  }

  // Validate steps
  if (workflow.steps) {
    for (let i = 0; i < workflow.steps.length; i++) {
      const step = workflow.steps[i];
      const stepErrors = validateWorkflowStep(step, `steps[${i}]`);
      errors.push(...stepErrors);
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Validate workflow step
 */
export function validateWorkflowStep(step: MCPWorkflowStep, path: string = ''): string[] {
  const errors: string[] = [];
  const prefix = path ? `${path}.` : '';

  if (!step.id) {
    errors.push(`${prefix}id is required`);
  }

  if (!step.type) {
    errors.push(`${prefix}type is required`);
  }

  switch (step.type) {
    case 'tool':
      if (!step.toolId) {
        errors.push(`${prefix}toolId is required for tool step`);
      }
      break;
    case 'prompt':
      if (!step.promptId) {
        errors.push(`${prefix}promptId is required for prompt step`);
      }
      break;
    case 'resource':
      if (!step.resourceId) {
        errors.push(`${prefix}resourceId is required for resource step`);
      }
      break;
    case 'condition':
      if (!step.condition) {
        errors.push(`${prefix}condition is required for condition step`);
      }
      if (!step.steps || step.steps.length === 0) {
        errors.push(`${prefix}steps are required for condition step`);
      }
      break;
    case 'parallel':
      if (!step.steps || step.steps.length === 0) {
        errors.push(`${prefix}steps are required for parallel step`);
      }
      break;
  }

  // Validate nested steps
  if (step.steps) {
    for (let i = 0; i < step.steps.length; i++) {
      const nestedStep = step.steps[i];
      const nestedErrors = validateWorkflowStep(nestedStep, `${prefix}steps[${i}]`);
      errors.push(...nestedErrors);
    }
  }

  return errors;
}

/**
 * Validate agent configuration
 */
export function validateAgentConfig(config: MCPAgentConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.name) {
    errors.push('Agent name is required');
  }

  if (!config.workflows || config.workflows.length === 0) {
    errors.push('Agent must have at least one workflow');
  }

  // Validate workflows
  if (config.workflows) {
    for (let i = 0; i < config.workflows.length; i++) {
      const workflow = config.workflows[i];
      const workflowValidation = validateWorkflow(workflow);
      if (!workflowValidation.valid) {
        errors.push(`workflows[${i}]: ${workflowValidation.errors.join(', ')}`);
      }
    }
  }

  // Validate limits
  if (config.maxIterations && config.maxIterations < 1) {
    errors.push('maxIterations must be greater than 0');
  }

  if (config.timeout && config.timeout < 1000) {
    errors.push('timeout must be at least 1000ms');
  }

  if (config.memorySize && config.memorySize < 1) {
    errors.push('memorySize must be greater than 0');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Create a default agent configuration
 */
export function createDefaultAgentConfig(name: string): MCPAgentConfig {
  return {
    name,
    description: `Default agent configuration for ${name}`,
    workflows: [],
    tools: [],
    resources: [],
    prompts: [],
    maxIterations: 100,
    timeout: 300000, // 5 minutes
    enableMemory: true,
    memorySize: 1000
  };
}

/**
 * Merge agent configurations
 */
export function mergeAgentConfigs(base: MCPAgentConfig, override: Partial<MCPAgentConfig>): MCPAgentConfig {
  return {
    ...base,
    ...override,
    workflows: override.workflows || base.workflows,
    tools: override.tools || base.tools,
    resources: override.resources || base.resources,
    prompts: override.prompts || base.prompts
  };
}

/**
 * Extract tool IDs from workflow
 */
export function extractToolIds(workflow: MCPWorkflow): string[] {
  const toolIds: string[] = [];

  function extractFromSteps(steps: MCPWorkflowStep[]) {
    for (const step of steps) {
      if (step.type === 'tool' && step.toolId) {
        toolIds.push(step.toolId);
      }
      if (step.steps) {
        extractFromSteps(step.steps);
      }
    }
  }

  extractFromSteps(workflow.steps);
  return [...new Set(toolIds)]; // Remove duplicates
}

/**
 * Extract prompt IDs from workflow
 */
export function extractPromptIds(workflow: MCPWorkflow): string[] {
  const promptIds: string[] = [];

  function extractFromSteps(steps: MCPWorkflowStep[]) {
    for (const step of steps) {
      if (step.type === 'prompt' && step.promptId) {
        promptIds.push(step.promptId);
      }
      if (step.steps) {
        extractFromSteps(step.steps);
      }
    }
  }

  extractFromSteps(workflow.steps);
  return [...new Set(promptIds)]; // Remove duplicates
}

/**
 * Extract resource IDs from workflow
 */
export function extractResourceIds(workflow: MCPWorkflow): string[] {
  const resourceIds: string[] = [];

  function extractFromSteps(steps: MCPWorkflowStep[]) {
    for (const step of steps) {
      if (step.type === 'resource' && step.resourceId) {
        resourceIds.push(step.resourceId);
      }
      if (step.steps) {
        extractFromSteps(step.steps);
      }
    }
  }

  extractFromSteps(workflow.steps);
  return [...new Set(resourceIds)]; // Remove duplicates
}
