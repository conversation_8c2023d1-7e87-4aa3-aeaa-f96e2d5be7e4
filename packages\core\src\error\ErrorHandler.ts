/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { EventEmitter } from 'events';
import type { Logger } from '@inkbytefo/s647-shared';

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * Error categories
 */
export enum ErrorCategory {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  VALIDATION = 'validation',
  RESOURCE = 'resource',
  TIMEOUT = 'timeout',
  RATE_LIMIT = 'rate_limit',
  SYSTEM = 'system',
  USER = 'user',
  UNKNOWN = 'unknown'
}

/**
 * Recovery strategy types
 */
export enum RecoveryStrategy {
  RETRY = 'retry',
  FALLBACK = 'fallback',
  CIRCUIT_BREAKER = 'circuit_breaker',
  GRACEFUL_DEGRADATION = 'graceful_degradation',
  MANUAL_INTERVENTION = 'manual_intervention',
  IGNORE = 'ignore'
}

/**
 * Error context information
 */
export interface ErrorContext {
  operation: string;
  component: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

/**
 * Enhanced error with recovery information
 */
export interface EnhancedError {
  id: string;
  originalError: Error;
  severity: ErrorSeverity;
  category: ErrorCategory;
  context: ErrorContext;
  recoveryStrategy: RecoveryStrategy;
  retryCount: number;
  maxRetries: number;
  isRecoverable: boolean;
  recoveryAttempts: RecoveryAttempt[];
  resolved: boolean;
  resolvedAt?: Date;
}

/**
 * Recovery attempt information
 */
export interface RecoveryAttempt {
  id: string;
  strategy: RecoveryStrategy;
  timestamp: Date;
  success: boolean;
  error?: string;
  duration: number;
  metadata?: Record<string, any>;
}

/**
 * Error handling configuration
 */
export interface ErrorHandlingConfig {
  maxRetries: number;
  retryDelay: number;
  exponentialBackoff: boolean;
  circuitBreakerThreshold: number;
  circuitBreakerTimeout: number;
  enableFallbacks: boolean;
  enableGracefulDegradation: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}

/**
 * Fallback function type
 */
export type FallbackFunction<T = any> = (error: EnhancedError, context: ErrorContext) => Promise<T>;

/**
 * Recovery function type
 */
export type RecoveryFunction = (error: EnhancedError) => Promise<boolean>;

/**
 * Comprehensive error handler with recovery mechanisms
 */
export class ErrorHandler extends EventEmitter {
  private config: ErrorHandlingConfig;
  private logger?: Logger;
  private errors: Map<string, EnhancedError> = new Map();
  private fallbacks: Map<string, FallbackFunction> = new Map();
  private recoveryFunctions: Map<RecoveryStrategy, RecoveryFunction> = new Map();
  private circuitBreakers: Map<string, CircuitBreakerState> = new Map();

  constructor(config: Partial<ErrorHandlingConfig> = {}, logger?: Logger) {
    super();
    this.config = {
      maxRetries: 3,
      retryDelay: 1000,
      exponentialBackoff: true,
      circuitBreakerThreshold: 5,
      circuitBreakerTimeout: 60000,
      enableFallbacks: true,
      enableGracefulDegradation: true,
      logLevel: 'error',
      ...config
    };
    this.logger = logger;
    this.setupDefaultRecoveryFunctions();
  }

  /**
   * Handle an error with automatic recovery
   */
  public async handleError<T = any>(
    error: Error,
    context: ErrorContext,
    fallbackValue?: T
  ): Promise<{ success: boolean; result?: T; error?: EnhancedError }> {
    const enhancedError = this.enhanceError(error, context);
    this.errors.set(enhancedError.id, enhancedError);

    this.emit('errorOccurred', enhancedError);
    this.logError(enhancedError);

    // Check circuit breaker
    if (this.isCircuitBreakerOpen(context.component)) {
      return this.handleCircuitBreakerOpen(enhancedError, fallbackValue);
    }

    // Attempt recovery
    const recoveryResult = await this.attemptRecovery(enhancedError);
    if (recoveryResult.success) {
      return { success: true, result: recoveryResult.result };
    }

    // Try fallback if available
    if (this.config.enableFallbacks) {
      const fallbackResult = await this.tryFallback(enhancedError, fallbackValue);
      if (fallbackResult.success) {
        return { success: true, result: fallbackResult.result };
      }
    }

    // Update circuit breaker
    this.updateCircuitBreaker(context.component, false);

    return { success: false, error: enhancedError };
  }

  /**
   * Execute operation with error handling
   */
  public async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    context: ErrorContext,
    fallbackValue?: T
  ): Promise<T> {
    try {
      const result = await operation();
      this.updateCircuitBreaker(context.component, true);
      return result;
    } catch (error) {
      const handlingResult = await this.handleError(error as Error, context, fallbackValue);
      
      if (handlingResult.success) {
        return handlingResult.result!;
      }

      throw handlingResult.error?.originalError || error;
    }
  }

  /**
   * Enhance error with additional information
   */
  private enhanceError(error: Error, context: ErrorContext): EnhancedError {
    const errorId = `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      id: errorId,
      originalError: error,
      severity: this.determineSeverity(error),
      category: this.categorizeError(error),
      context,
      recoveryStrategy: this.determineRecoveryStrategy(error),
      retryCount: 0,
      maxRetries: this.config.maxRetries,
      isRecoverable: this.isRecoverable(error),
      recoveryAttempts: [],
      resolved: false
    };
  }

  /**
   * Determine error severity
   */
  private determineSeverity(error: Error): ErrorSeverity {
    const message = error.message.toLowerCase();
    
    if (message.includes('critical') || message.includes('fatal')) {
      return ErrorSeverity.CRITICAL;
    }
    if (message.includes('timeout') || message.includes('network')) {
      return ErrorSeverity.HIGH;
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return ErrorSeverity.MEDIUM;
    }
    
    return ErrorSeverity.LOW;
  }

  /**
   * Categorize error
   */
  private categorizeError(error: Error): ErrorCategory {
    const message = error.message.toLowerCase();
    const name = error.name.toLowerCase();
    
    if (message.includes('network') || message.includes('connection')) {
      return ErrorCategory.NETWORK;
    }
    if (message.includes('auth') || message.includes('unauthorized')) {
      return ErrorCategory.AUTHENTICATION;
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return ErrorCategory.VALIDATION;
    }
    if (message.includes('timeout')) {
      return ErrorCategory.TIMEOUT;
    }
    if (message.includes('rate limit') || message.includes('too many requests')) {
      return ErrorCategory.RATE_LIMIT;
    }
    if (message.includes('not found') || message.includes('resource')) {
      return ErrorCategory.RESOURCE;
    }
    if (name.includes('system') || message.includes('system')) {
      return ErrorCategory.SYSTEM;
    }
    
    return ErrorCategory.UNKNOWN;
  }

  /**
   * Determine recovery strategy
   */
  private determineRecoveryStrategy(error: Error): RecoveryStrategy {
    const category = this.categorizeError(error);
    const severity = this.determineSeverity(error);
    
    switch (category) {
      case ErrorCategory.NETWORK:
      case ErrorCategory.TIMEOUT:
        return RecoveryStrategy.RETRY;
      case ErrorCategory.RATE_LIMIT:
        return RecoveryStrategy.CIRCUIT_BREAKER;
      case ErrorCategory.AUTHENTICATION:
        return RecoveryStrategy.MANUAL_INTERVENTION;
      case ErrorCategory.VALIDATION:
      case ErrorCategory.USER:
        return RecoveryStrategy.IGNORE;
      case ErrorCategory.RESOURCE:
        return RecoveryStrategy.FALLBACK;
      default:
        return severity === ErrorSeverity.CRITICAL 
          ? RecoveryStrategy.MANUAL_INTERVENTION 
          : RecoveryStrategy.RETRY;
    }
  }

  /**
   * Check if error is recoverable
   */
  private isRecoverable(error: Error): boolean {
    const category = this.categorizeError(error);
    const nonRecoverableCategories = [
      ErrorCategory.VALIDATION,
      ErrorCategory.AUTHENTICATION
    ];
    
    return !nonRecoverableCategories.includes(category);
  }

  /**
   * Attempt recovery
   */
  private async attemptRecovery(error: EnhancedError): Promise<{ success: boolean; result?: any }> {
    if (!error.isRecoverable || error.retryCount >= error.maxRetries) {
      return { success: false };
    }

    const recoveryFunction = this.recoveryFunctions.get(error.recoveryStrategy);
    if (!recoveryFunction) {
      return { success: false };
    }

    const attemptId = `attempt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    try {
      error.retryCount++;
      
      // Apply delay with exponential backoff
      const delay = this.calculateRetryDelay(error.retryCount);
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      const success = await recoveryFunction(error);
      
      const attempt: RecoveryAttempt = {
        id: attemptId,
        strategy: error.recoveryStrategy,
        timestamp: new Date(),
        success,
        duration: Date.now() - startTime
      };

      error.recoveryAttempts.push(attempt);

      if (success) {
        error.resolved = true;
        error.resolvedAt = new Date();
        this.emit('errorRecovered', error);
        this.logger?.info(`Error recovered: ${error.id}`);
      }

      return { success };

    } catch (recoveryError) {
      const attempt: RecoveryAttempt = {
        id: attemptId,
        strategy: error.recoveryStrategy,
        timestamp: new Date(),
        success: false,
        error: (recoveryError as Error).message,
        duration: Date.now() - startTime
      };

      error.recoveryAttempts.push(attempt);
      this.logger?.warn(`Recovery attempt failed: ${error.id}`, recoveryError);

      return { success: false };
    }
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(retryCount: number): number {
    if (!this.config.exponentialBackoff) {
      return this.config.retryDelay;
    }

    return this.config.retryDelay * Math.pow(2, retryCount - 1);
  }

  /**
   * Try fallback
   */
  private async tryFallback<T>(
    error: EnhancedError,
    fallbackValue?: T
  ): Promise<{ success: boolean; result?: T }> {
    const fallbackKey = `${error.context.component}.${error.context.operation}`;
    const fallbackFunction = this.fallbacks.get(fallbackKey) || this.fallbacks.get(error.context.component);

    if (fallbackFunction) {
      try {
        const result = await fallbackFunction(error, error.context);
        this.logger?.info(`Fallback successful for error: ${error.id}`);
        this.emit('fallbackUsed', error, result);
        return { success: true, result };
      } catch (fallbackError) {
        this.logger?.warn(`Fallback failed for error: ${error.id}`, fallbackError);
      }
    }

    if (fallbackValue !== undefined) {
      this.logger?.info(`Using provided fallback value for error: ${error.id}`);
      return { success: true, result: fallbackValue };
    }

    return { success: false };
  }

  /**
   * Setup default recovery functions
   */
  private setupDefaultRecoveryFunctions(): void {
    // Retry recovery
    this.recoveryFunctions.set(RecoveryStrategy.RETRY, async (error) => {
      // Simple retry - in production, this might involve more sophisticated logic
      return error.retryCount <= error.maxRetries;
    });

    // Circuit breaker recovery
    this.recoveryFunctions.set(RecoveryStrategy.CIRCUIT_BREAKER, async (error) => {
      const component = error.context.component;
      const breaker = this.circuitBreakers.get(component);
      
      if (breaker && breaker.state === 'open') {
        return false; // Circuit is open, don't retry
      }
      
      return true;
    });

    // Fallback recovery
    this.recoveryFunctions.set(RecoveryStrategy.FALLBACK, async (error) => {
      // Always try fallback if available
      return true;
    });

    // Graceful degradation
    this.recoveryFunctions.set(RecoveryStrategy.GRACEFUL_DEGRADATION, async (error) => {
      if (this.config.enableGracefulDegradation) {
        this.emit('gracefulDegradation', error);
        return true;
      }
      return false;
    });
  }

  /**
   * Register fallback function
   */
  public registerFallback<T>(key: string, fallbackFunction: FallbackFunction<T>): void {
    this.fallbacks.set(key, fallbackFunction);
    this.logger?.debug(`Registered fallback for: ${key}`);
  }

  /**
   * Register recovery function
   */
  public registerRecoveryFunction(strategy: RecoveryStrategy, recoveryFunction: RecoveryFunction): void {
    this.recoveryFunctions.set(strategy, recoveryFunction);
    this.logger?.debug(`Registered recovery function for strategy: ${strategy}`);
  }

  /**
   * Circuit breaker management
   */
  private isCircuitBreakerOpen(component: string): boolean {
    const breaker = this.circuitBreakers.get(component);
    if (!breaker) return false;

    if (breaker.state === 'open') {
      // Check if timeout has passed
      if (Date.now() - breaker.lastFailureTime > this.config.circuitBreakerTimeout) {
        breaker.state = 'half-open';
        breaker.failureCount = 0;
      } else {
        return true;
      }
    }

    return false;
  }

  private updateCircuitBreaker(component: string, success: boolean): void {
    let breaker = this.circuitBreakers.get(component);
    if (!breaker) {
      breaker = {
        state: 'closed',
        failureCount: 0,
        lastFailureTime: 0
      };
      this.circuitBreakers.set(component, breaker);
    }

    if (success) {
      breaker.failureCount = 0;
      if (breaker.state === 'half-open') {
        breaker.state = 'closed';
      }
    } else {
      breaker.failureCount++;
      breaker.lastFailureTime = Date.now();

      if (breaker.failureCount >= this.config.circuitBreakerThreshold) {
        breaker.state = 'open';
        this.emit('circuitBreakerOpened', component);
        this.logger?.warn(`Circuit breaker opened for component: ${component}`);
      }
    }
  }

  private handleCircuitBreakerOpen<T>(
    error: EnhancedError,
    fallbackValue?: T
  ): { success: boolean; result?: T; error?: EnhancedError } {
    this.logger?.warn(`Circuit breaker is open for component: ${error.context.component}`);
    
    if (fallbackValue !== undefined) {
      return { success: true, result: fallbackValue };
    }

    return { success: false, error };
  }

  /**
   * Log error based on severity
   */
  private logError(error: EnhancedError): void {
    const logMessage = `Error ${error.id}: ${error.originalError.message}`;
    const logData = {
      errorId: error.id,
      severity: error.severity,
      category: error.category,
      context: error.context,
      recoveryStrategy: error.recoveryStrategy
    };

    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        this.logger?.error(logMessage, logData);
        break;
      case ErrorSeverity.HIGH:
        this.logger?.error(logMessage, logData);
        break;
      case ErrorSeverity.MEDIUM:
        this.logger?.warn(logMessage, logData);
        break;
      case ErrorSeverity.LOW:
        this.logger?.info(logMessage, logData);
        break;
    }
  }

  /**
   * Get error statistics
   */
  public getErrorStatistics(): any {
    const errors = Array.from(this.errors.values());
    const now = Date.now();
    const oneHourAgo = now - 3600000;

    const recentErrors = errors.filter(e => e.context.timestamp.getTime() > oneHourAgo);
    const resolvedErrors = errors.filter(e => e.resolved);

    return {
      totalErrors: errors.length,
      recentErrors: recentErrors.length,
      resolvedErrors: resolvedErrors.length,
      resolutionRate: errors.length > 0 ? resolvedErrors.length / errors.length : 0,
      errorsByCategory: this.groupBy(errors, 'category'),
      errorsBySeverity: this.groupBy(errors, 'severity'),
      circuitBreakers: Object.fromEntries(this.circuitBreakers)
    };
  }

  private groupBy(array: any[], key: string): Record<string, number> {
    return array.reduce((acc, item) => {
      const value = item[key];
      acc[value] = (acc[value] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * Clear resolved errors
   */
  public clearResolvedErrors(): number {
    const resolvedCount = Array.from(this.errors.values()).filter(e => e.resolved).length;
    
    for (const [id, error] of this.errors) {
      if (error.resolved) {
        this.errors.delete(id);
      }
    }

    return resolvedCount;
  }
}

/**
 * Circuit breaker state
 */
interface CircuitBreakerState {
  state: 'closed' | 'open' | 'half-open';
  failureCount: number;
  lastFailureTime: number;
}
