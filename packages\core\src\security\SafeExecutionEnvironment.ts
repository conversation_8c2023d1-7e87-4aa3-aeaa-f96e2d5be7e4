/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { EventEmitter } from 'events';
import type { Logger } from '@inkbytefo/s647-shared';
import { SecurityManager, type SecurityContext } from './SecurityManager.js';

/**
 * Execution permission levels
 */
export enum PermissionLevel {
  NONE = 'none',
  READ_ONLY = 'read_only',
  LIMITED = 'limited',
  STANDARD = 'standard',
  ELEVATED = 'elevated',
  ADMIN = 'admin'
}

/**
 * Execution context
 */
export interface ExecutionContext {
  userId?: string;
  sessionId?: string;
  permissionLevel: PermissionLevel;
  allowedOperations: string[];
  blockedOperations: string[];
  resourceLimits: ResourceLimits;
  timeoutMs: number;
  sandboxed: boolean;
  auditEnabled: boolean;
}

/**
 * Resource limits
 */
export interface ResourceLimits {
  maxMemoryMB: number;
  maxCpuPercent: number;
  maxExecutionTimeMs: number;
  maxFileSize: number;
  maxNetworkRequests: number;
  allowedPaths: string[];
  blockedPaths: string[];
}

/**
 * Execution result
 */
export interface ExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  duration: number;
  resourceUsage: ResourceUsage;
  securityViolations: any[];
  auditLog: AuditEntry[];
}

/**
 * Resource usage
 */
export interface ResourceUsage {
  memoryUsedMB: number;
  cpuUsedPercent: number;
  executionTimeMs: number;
  filesAccessed: string[];
  networkRequests: number;
}

/**
 * Audit entry
 */
export interface AuditEntry {
  timestamp: Date;
  action: string;
  resource?: string;
  result: 'allowed' | 'denied' | 'error';
  details?: string;
}

/**
 * Safe operation
 */
export interface SafeOperation {
  id: string;
  name: string;
  description: string;
  requiredPermission: PermissionLevel;
  resourceRequirements: Partial<ResourceLimits>;
  validator?: (params: any, context: ExecutionContext) => boolean;
  executor: (params: any, context: ExecutionContext) => Promise<any>;
}

/**
 * Safe execution environment for running operations with security controls
 */
export class SafeExecutionEnvironment extends EventEmitter {
  private securityManager: SecurityManager;
  private logger?: Logger;
  private operations: Map<string, SafeOperation> = new Map();
  private activeExecutions: Map<string, ExecutionMonitor> = new Map();
  private defaultLimits: ResourceLimits;

  constructor(securityManager: SecurityManager, logger?: Logger) {
    super();
    this.securityManager = securityManager;
    this.logger = logger;
    this.defaultLimits = {
      maxMemoryMB: 512,
      maxCpuPercent: 50,
      maxExecutionTimeMs: 30000,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxNetworkRequests: 10,
      allowedPaths: ['/tmp', '/var/tmp'],
      blockedPaths: ['/etc', '/bin', '/usr/bin', '/system']
    };
    this.setupDefaultOperations();
  }

  /**
   * Execute operation safely
   */
  public async executeOperation(
    operationId: string,
    params: any,
    context: ExecutionContext
  ): Promise<ExecutionResult> {
    const operation = this.operations.get(operationId);
    if (!operation) {
      throw new Error(`Operation not found: ${operationId}`);
    }

    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();
    const auditLog: AuditEntry[] = [];
    const resourceUsage: ResourceUsage = {
      memoryUsedMB: 0,
      cpuUsedPercent: 0,
      executionTimeMs: 0,
      filesAccessed: [],
      networkRequests: 0
    };

    // Create security context
    const securityContext: SecurityContext = {
      userId: context.userId,
      sessionId: context.sessionId,
      source: 'safe_execution_environment',
      operation: operationId,
      timestamp: new Date(),
      metadata: { permissionLevel: context.permissionLevel }
    };

    try {
      // Pre-execution security checks
      const preCheckResult = await this.performPreExecutionChecks(
        operation,
        params,
        context,
        securityContext
      );

      if (!preCheckResult.allowed) {
        return {
          success: false,
          error: preCheckResult.reason,
          duration: Date.now() - startTime,
          resourceUsage,
          securityViolations: preCheckResult.violations || [],
          auditLog: [
            {
              timestamp: new Date(),
              action: 'pre_execution_check',
              result: 'denied',
              details: preCheckResult.reason
            }
          ]
        };
      }

      auditLog.push({
        timestamp: new Date(),
        action: 'pre_execution_check',
        result: 'allowed'
      });

      // Create execution monitor
      const monitor = new ExecutionMonitor(context.resourceLimits, context.timeoutMs);
      this.activeExecutions.set(executionId, monitor);

      // Start monitoring
      monitor.start();

      try {
        // Execute operation
        auditLog.push({
          timestamp: new Date(),
          action: 'operation_start',
          resource: operationId,
          result: 'allowed'
        });

        const result = await this.executeWithMonitoring(
          operation,
          params,
          context,
          monitor,
          auditLog
        );

        // Stop monitoring
        monitor.stop();
        resourceUsage.memoryUsedMB = monitor.getMaxMemoryUsage();
        resourceUsage.cpuUsedPercent = monitor.getMaxCpuUsage();
        resourceUsage.executionTimeMs = Date.now() - startTime;

        auditLog.push({
          timestamp: new Date(),
          action: 'operation_complete',
          resource: operationId,
          result: 'allowed'
        });

        this.emit('operationCompleted', {
          operationId,
          executionId,
          success: true,
          duration: resourceUsage.executionTimeMs
        });

        return {
          success: true,
          result,
          duration: resourceUsage.executionTimeMs,
          resourceUsage,
          securityViolations: [],
          auditLog
        };

      } catch (error) {
        monitor.stop();
        resourceUsage.executionTimeMs = Date.now() - startTime;

        auditLog.push({
          timestamp: new Date(),
          action: 'operation_error',
          resource: operationId,
          result: 'error',
          details: (error as Error).message
        });

        this.emit('operationFailed', {
          operationId,
          executionId,
          error: (error as Error).message,
          duration: resourceUsage.executionTimeMs
        });

        return {
          success: false,
          error: (error as Error).message,
          duration: resourceUsage.executionTimeMs,
          resourceUsage,
          securityViolations: [],
          auditLog
        };
      }

    } finally {
      this.activeExecutions.delete(executionId);
    }
  }

  /**
   * Perform pre-execution security checks
   */
  private async performPreExecutionChecks(
    operation: SafeOperation,
    params: any,
    context: ExecutionContext,
    securityContext: SecurityContext
  ): Promise<{ allowed: boolean; reason?: string; violations?: any[] }> {
    // Check permission level
    if (!this.hasRequiredPermission(context.permissionLevel, operation.requiredPermission)) {
      return {
        allowed: false,
        reason: `Insufficient permissions. Required: ${operation.requiredPermission}, Current: ${context.permissionLevel}`
      };
    }

    // Check if operation is allowed
    if (context.blockedOperations.includes(operation.id)) {
      return {
        allowed: false,
        reason: `Operation is blocked: ${operation.id}`
      };
    }

    if (context.allowedOperations.length > 0 && !context.allowedOperations.includes(operation.id)) {
      return {
        allowed: false,
        reason: `Operation not in allowed list: ${operation.id}`
      };
    }

    // Validate input parameters
    const inputValidation = this.securityManager.validateInput(params, securityContext);
    if (!inputValidation.valid) {
      return {
        allowed: false,
        reason: 'Input validation failed',
        violations: inputValidation.violations
      };
    }

    // Check operation-specific validator
    if (operation.validator && !operation.validator(params, context)) {
      return {
        allowed: false,
        reason: 'Operation-specific validation failed'
      };
    }

    // Check resource requirements
    const resourceCheck = this.checkResourceRequirements(operation, context);
    if (!resourceCheck.allowed) {
      return resourceCheck;
    }

    return { allowed: true };
  }

  /**
   * Check if user has required permission
   */
  private hasRequiredPermission(current: PermissionLevel, required: PermissionLevel): boolean {
    const levels = [
      PermissionLevel.NONE,
      PermissionLevel.READ_ONLY,
      PermissionLevel.LIMITED,
      PermissionLevel.STANDARD,
      PermissionLevel.ELEVATED,
      PermissionLevel.ADMIN
    ];

    const currentIndex = levels.indexOf(current);
    const requiredIndex = levels.indexOf(required);

    return currentIndex >= requiredIndex;
  }

  /**
   * Check resource requirements
   */
  private checkResourceRequirements(
    operation: SafeOperation,
    context: ExecutionContext
  ): { allowed: boolean; reason?: string } {
    const requirements = operation.resourceRequirements;
    const limits = context.resourceLimits;

    if (requirements.maxMemoryMB && requirements.maxMemoryMB > limits.maxMemoryMB) {
      return {
        allowed: false,
        reason: `Memory requirement exceeds limit: ${requirements.maxMemoryMB}MB > ${limits.maxMemoryMB}MB`
      };
    }

    if (requirements.maxExecutionTimeMs && requirements.maxExecutionTimeMs > limits.maxExecutionTimeMs) {
      return {
        allowed: false,
        reason: `Execution time requirement exceeds limit: ${requirements.maxExecutionTimeMs}ms > ${limits.maxExecutionTimeMs}ms`
      };
    }

    return { allowed: true };
  }

  /**
   * Execute operation with monitoring
   */
  private async executeWithMonitoring(
    operation: SafeOperation,
    params: any,
    context: ExecutionContext,
    monitor: ExecutionMonitor,
    auditLog: AuditEntry[]
  ): Promise<any> {
    // Create sandboxed execution context if required
    if (context.sandboxed) {
      return await this.executeInSandbox(operation, params, context, monitor, auditLog);
    } else {
      return await operation.executor(params, context);
    }
  }

  /**
   * Execute operation in sandbox
   */
  private async executeInSandbox(
    operation: SafeOperation,
    params: any,
    context: ExecutionContext,
    monitor: ExecutionMonitor,
    auditLog: AuditEntry[]
  ): Promise<any> {
    // Create isolated execution environment
    const sandboxContext = {
      ...context,
      // Override dangerous functions
      require: undefined,
      import: undefined,
      eval: undefined,
      Function: undefined,
      // Provide safe alternatives
      console: {
        log: (...args: any[]) => this.logger?.info('Sandbox log:', ...args),
        warn: (...args: any[]) => this.logger?.warn('Sandbox warn:', ...args),
        error: (...args: any[]) => this.logger?.error('Sandbox error:', ...args)
      }
    };

    auditLog.push({
      timestamp: new Date(),
      action: 'sandbox_created',
      result: 'allowed'
    });

    try {
      // Execute in controlled environment
      const result = await operation.executor(params, sandboxContext);
      
      auditLog.push({
        timestamp: new Date(),
        action: 'sandbox_execution',
        result: 'allowed'
      });

      return result;
    } catch (error) {
      auditLog.push({
        timestamp: new Date(),
        action: 'sandbox_execution',
        result: 'error',
        details: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Register safe operation
   */
  public registerOperation(operation: SafeOperation): void {
    this.operations.set(operation.id, operation);
    this.logger?.debug(`Registered safe operation: ${operation.id}`);
  }

  /**
   * Unregister operation
   */
  public unregisterOperation(operationId: string): boolean {
    const removed = this.operations.delete(operationId);
    if (removed) {
      this.logger?.debug(`Unregistered operation: ${operationId}`);
    }
    return removed;
  }

  /**
   * Get available operations for permission level
   */
  public getAvailableOperations(permissionLevel: PermissionLevel): SafeOperation[] {
    return Array.from(this.operations.values())
      .filter(op => this.hasRequiredPermission(permissionLevel, op.requiredPermission));
  }

  /**
   * Create execution context
   */
  public createExecutionContext(
    permissionLevel: PermissionLevel,
    options: Partial<ExecutionContext> = {}
  ): ExecutionContext {
    return {
      permissionLevel,
      allowedOperations: [],
      blockedOperations: [],
      resourceLimits: { ...this.defaultLimits },
      timeoutMs: 30000,
      sandboxed: true,
      auditEnabled: true,
      ...options
    };
  }

  /**
   * Setup default safe operations
   */
  private setupDefaultOperations(): void {
    // File read operation
    this.registerOperation({
      id: 'file_read',
      name: 'Read File',
      description: 'Safely read file contents',
      requiredPermission: PermissionLevel.READ_ONLY,
      resourceRequirements: {
        maxFileSize: 1024 * 1024 // 1MB
      },
      validator: (params, context) => {
        const path = params.path;
        return typeof path === 'string' && 
               context.resourceLimits.allowedPaths.some(allowed => path.startsWith(allowed));
      },
      executor: async (params, context) => {
        // Simplified file read implementation
        // In production, use proper file system APIs with security checks
        return `File content from: ${params.path}`;
      }
    });

    // HTTP request operation
    this.registerOperation({
      id: 'http_request',
      name: 'HTTP Request',
      description: 'Make HTTP requests with safety checks',
      requiredPermission: PermissionLevel.LIMITED,
      resourceRequirements: {
        maxNetworkRequests: 5
      },
      validator: (params, context) => {
        const url = params.url;
        return typeof url === 'string' && 
               (url.startsWith('https://') || url.startsWith('http://'));
      },
      executor: async (params, context) => {
        // Simplified HTTP request implementation
        // In production, use proper HTTP client with security checks
        return { status: 200, data: `Response from: ${params.url}` };
      }
    });

    // Data processing operation
    this.registerOperation({
      id: 'data_process',
      name: 'Process Data',
      description: 'Process data with safety checks',
      requiredPermission: PermissionLevel.STANDARD,
      resourceRequirements: {
        maxMemoryMB: 256,
        maxExecutionTimeMs: 10000
      },
      executor: async (params, context) => {
        // Simplified data processing
        return { processed: true, input: params };
      }
    });

    this.logger?.debug('Default safe operations registered');
  }

  /**
   * Get execution statistics
   */
  public getExecutionStatistics(): {
    totalOperations: number;
    activeExecutions: number;
    registeredOperations: number;
    operationsByPermission: Record<PermissionLevel, number>;
  } {
    const operationsByPermission = {} as Record<PermissionLevel, number>;
    
    for (const operation of this.operations.values()) {
      operationsByPermission[operation.requiredPermission] = 
        (operationsByPermission[operation.requiredPermission] || 0) + 1;
    }

    return {
      totalOperations: this.operations.size,
      activeExecutions: this.activeExecutions.size,
      registeredOperations: this.operations.size,
      operationsByPermission
    };
  }

  /**
   * Get active executions
   */
  public getActiveExecutions(): string[] {
    return Array.from(this.activeExecutions.keys());
  }

  /**
   * Force stop execution
   */
  public forceStopExecution(executionId: string): boolean {
    const monitor = this.activeExecutions.get(executionId);
    if (monitor) {
      monitor.forceStop();
      this.activeExecutions.delete(executionId);
      this.logger?.warn(`Forced stop execution: ${executionId}`);
      return true;
    }
    return false;
  }
}

/**
 * Execution monitor for tracking resource usage
 */
class ExecutionMonitor {
  private limits: ResourceLimits;
  private timeoutMs: number;
  private startTime: number = 0;
  private maxMemoryUsage: number = 0;
  private maxCpuUsage: number = 0;
  private monitoring: boolean = false;
  private monitoringInterval?: NodeJS.Timeout;
  private timeoutTimer?: NodeJS.Timeout;

  constructor(limits: ResourceLimits, timeoutMs: number) {
    this.limits = limits;
    this.timeoutMs = timeoutMs;
  }

  public start(): void {
    this.startTime = Date.now();
    this.monitoring = true;

    // Start resource monitoring
    this.monitoringInterval = setInterval(() => {
      this.checkResources();
    }, 1000);

    // Set timeout
    this.timeoutTimer = setTimeout(() => {
      this.forceStop();
    }, this.timeoutMs);
  }

  public stop(): void {
    this.monitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer);
      this.timeoutTimer = undefined;
    }
  }

  public forceStop(): void {
    this.stop();
    // In production, implement actual process termination
  }

  private checkResources(): void {
    if (!this.monitoring) return;

    // Check memory usage (simplified)
    const memoryUsage = process.memoryUsage();
    const currentMemoryMB = memoryUsage.heapUsed / 1024 / 1024;
    this.maxMemoryUsage = Math.max(this.maxMemoryUsage, currentMemoryMB);

    if (currentMemoryMB > this.limits.maxMemoryMB) {
      throw new Error(`Memory limit exceeded: ${currentMemoryMB}MB > ${this.limits.maxMemoryMB}MB`);
    }

    // Check execution time
    const executionTime = Date.now() - this.startTime;
    if (executionTime > this.limits.maxExecutionTimeMs) {
      throw new Error(`Execution time limit exceeded: ${executionTime}ms > ${this.limits.maxExecutionTimeMs}ms`);
    }
  }

  public getMaxMemoryUsage(): number {
    return this.maxMemoryUsage;
  }

  public getMaxCpuUsage(): number {
    return this.maxCpuUsage;
  }
}
