/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { EventEmitter } from 'events';
import type { Logger } from '@inkbytefo/s647-shared';
import type {
  MCPAgentConfig,
  MCPAgentState,
  MCPAgentResult,
  MCPAgentContext,
  MCPWorkflow,
  MCPWorkflowStep,
  MCPWorkflowError,
  MCPAgentEvent
} from '../types/index.js';
import { MCPClient } from '../client/MCPClient.js';

/**
 * Autonomous MCP Agent that can execute workflows using MCP tools
 * Implements Anthropic's agent patterns for autonomous operation
 */
export class MCPAgent extends EventEmitter {
  private config: MCPAgentConfig;
  private client: MCPClient;
  private logger?: Logger;
  private state: MCPAgentState;
  private context: MCPAgentContext;
  private isRunning: boolean = false;

  constructor(config: MCPAgentConfig, client: MCPClient, logger?: Logger) {
    super();
    this.config = config;
    this.client = client;
    this.logger = logger;
    this.context = client.getAgentContext();
    
    this.state = {
      id: `agent-${Date.now()}`,
      status: 'idle',
      variables: {},
      memory: [],
      iterations: 0,
      startTime: new Date(),
      lastActivity: new Date()
    };
  }

  /**
   * Execute a workflow autonomously
   */
  public async executeWorkflow(workflowId: string, initialVariables?: Record<string, any>): Promise<MCPAgentResult> {
    const workflow = this.config.workflows.find(w => w.id === workflowId);
    if (!workflow) {
      throw new Error(`Workflow not found: ${workflowId}`);
    }

    this.isRunning = true;
    this.state.status = 'thinking';
    this.state.currentWorkflow = workflowId;
    this.state.variables = { ...initialVariables };
    this.state.iterations = 0;
    this.state.startTime = new Date();

    const result: MCPAgentResult = {
      success: false,
      steps: [],
      totalDuration: 0,
      iterations: 0
    };

    try {
      this.emitEvent('agent.started', { workflowId });
      this.logger?.info(`Starting workflow execution: ${workflowId}`);

      const startTime = Date.now();
      
      // Execute workflow steps
      await this.executeWorkflowSteps(workflow, result);
      
      result.totalDuration = Date.now() - startTime;
      result.iterations = this.state.iterations;
      result.success = true;

      this.state.status = 'complete';
      this.emitEvent('agent.completed', { workflowId, result });
      this.logger?.info(`Workflow completed: ${workflowId}`);

    } catch (error) {
      result.error = (error as Error).message;
      this.state.status = 'error';
      this.state.error = result.error;
      
      this.emitEvent('agent.error', { workflowId, error: result.error });
      this.logger?.error(`Workflow failed: ${workflowId}`, error);
    } finally {
      this.isRunning = false;
      this.state.lastActivity = new Date();
    }

    return result;
  }

  /**
   * Execute workflow steps recursively
   */
  private async executeWorkflowSteps(workflow: MCPWorkflow, result: MCPAgentResult): Promise<void> {
    for (const step of workflow.steps) {
      if (!this.isRunning) break;

      this.state.iterations++;
      if (this.config.maxIterations && this.state.iterations > this.config.maxIterations) {
        throw new MCPWorkflowError(workflow.id, step.id, new Error('Maximum iterations exceeded'));
      }

      await this.executeStep(workflow, step, result);
    }
  }

  /**
   * Execute a single workflow step
   */
  private async executeStep(workflow: MCPWorkflow, step: MCPWorkflowStep, result: MCPAgentResult): Promise<void> {
    const stepStartTime = Date.now();
    this.state.currentStep = step.id;
    this.state.status = 'executing';

    this.emitEvent('agent.step', { 
      workflowId: workflow.id, 
      stepId: step.id, 
      type: step.type 
    });

    try {
      let stepResult: any;

      switch (step.type) {
        case 'tool':
          stepResult = await this.executeToolStep(step);
          break;
        case 'prompt':
          stepResult = await this.executePromptStep(step);
          break;
        case 'resource':
          stepResult = await this.executeResourceStep(step);
          break;
        case 'condition':
          stepResult = await this.executeConditionStep(workflow, step, result);
          break;
        case 'parallel':
          stepResult = await this.executeParallelStep(workflow, step, result);
          break;
        default:
          throw new Error(`Unknown step type: ${step.type}`);
      }

      // Store result in variables if specified
      if (step.id) {
        this.state.variables[step.id] = stepResult;
      }

      result.steps.push({
        stepId: step.id,
        type: step.type,
        status: 'success',
        result: stepResult,
        duration: Date.now() - stepStartTime
      });

      // Add to memory if enabled
      if (this.config.enableMemory) {
        this.addToMemory({
          type: 'step_result',
          stepId: step.id,
          stepType: step.type,
          result: stepResult,
          timestamp: new Date()
        });
      }

    } catch (error) {
      result.steps.push({
        stepId: step.id,
        type: step.type,
        status: 'error',
        error: (error as Error).message,
        duration: Date.now() - stepStartTime
      });

      // Handle error based on step configuration
      if (step.onError) {
        this.logger?.warn(`Step ${step.id} failed, executing error handler: ${step.onError}`);
        // Could implement error handling workflow here
      } else {
        throw new MCPWorkflowError(workflow.id, step.id, error as Error);
      }
    }
  }

  /**
   * Execute a tool step
   */
  private async executeToolStep(step: MCPWorkflowStep): Promise<any> {
    if (!step.toolId) {
      throw new Error('Tool ID is required for tool step');
    }

    const toolId = this.resolveVariables(step.toolId);
    const params = this.resolveVariables(step.params || {});

    this.logger?.debug(`Executing tool: ${toolId}`, params);
    const result = await this.client.executeTool(toolId, params);
    
    return result.content?.[0]?.text || result;
  }

  /**
   * Execute a prompt step
   */
  private async executePromptStep(step: MCPWorkflowStep): Promise<any> {
    if (!step.promptId) {
      throw new Error('Prompt ID is required for prompt step');
    }

    const promptId = this.resolveVariables(step.promptId);
    const args = this.resolveVariables(step.params || {});

    this.logger?.debug(`Getting prompt: ${promptId}`, args);
    const result = await this.client.getPrompt(promptId, args);
    
    return result.messages || result;
  }

  /**
   * Execute a resource step
   */
  private async executeResourceStep(step: MCPWorkflowStep): Promise<any> {
    if (!step.resourceId) {
      throw new Error('Resource ID is required for resource step');
    }

    const resourceId = this.resolveVariables(step.resourceId);

    this.logger?.debug(`Reading resource: ${resourceId}`);
    const result = await this.client.readResource(resourceId);
    
    return result.contents || result;
  }

  /**
   * Execute a condition step
   */
  private async executeConditionStep(workflow: MCPWorkflow, step: MCPWorkflowStep, result: MCPAgentResult): Promise<any> {
    if (!step.condition) {
      throw new Error('Condition is required for condition step');
    }

    const condition = this.resolveVariables(step.condition);
    const conditionResult = this.evaluateCondition(condition);

    this.logger?.debug(`Evaluating condition: ${condition} = ${conditionResult}`);

    if (conditionResult && step.steps) {
      // Execute nested steps if condition is true
      for (const nestedStep of step.steps) {
        await this.executeStep(workflow, nestedStep, result);
      }
    }

    return conditionResult;
  }

  /**
   * Execute parallel steps
   */
  private async executeParallelStep(workflow: MCPWorkflow, step: MCPWorkflowStep, result: MCPAgentResult): Promise<any> {
    if (!step.steps) {
      throw new Error('Steps are required for parallel step');
    }

    this.logger?.debug(`Executing ${step.steps.length} steps in parallel`);

    const parallelPromises = step.steps.map(async (nestedStep) => {
      const nestedResult: MCPAgentResult = {
        success: false,
        steps: [],
        totalDuration: 0,
        iterations: 0
      };
      
      await this.executeStep(workflow, nestedStep, nestedResult);
      return nestedResult;
    });

    const parallelResults = await Promise.allSettled(parallelPromises);
    
    // Merge results
    for (const parallelResult of parallelResults) {
      if (parallelResult.status === 'fulfilled') {
        result.steps.push(...parallelResult.value.steps);
      } else {
        result.steps.push({
          stepId: `parallel-error-${Date.now()}`,
          type: 'parallel',
          status: 'error',
          error: parallelResult.reason?.message || 'Parallel step failed',
          duration: 0
        });
      }
    }

    return parallelResults;
  }

  /**
   * Resolve variables in strings and objects
   */
  private resolveVariables(input: any): any {
    if (typeof input === 'string') {
      return input.replace(/\$\{([^}]+)\}/g, (match, varName) => {
        return this.state.variables[varName] || match;
      });
    }

    if (Array.isArray(input)) {
      return input.map(item => this.resolveVariables(item));
    }

    if (typeof input === 'object' && input !== null) {
      const resolved: any = {};
      for (const [key, value] of Object.entries(input)) {
        resolved[key] = this.resolveVariables(value);
      }
      return resolved;
    }

    return input;
  }

  /**
   * Evaluate a simple condition
   */
  private evaluateCondition(condition: string): boolean {
    try {
      // Simple condition evaluation - in production, use a safer evaluator
      // This is a basic implementation for demonstration
      const variables = this.state.variables;
      
      // Replace variable references
      const evaluableCondition = condition.replace(/\$\{([^}]+)\}/g, (match, varName) => {
        const value = variables[varName];
        return typeof value === 'string' ? `"${value}"` : String(value);
      });

      // Use Function constructor for evaluation (safer than eval)
      return new Function('return ' + evaluableCondition)();
    } catch (error) {
      this.logger?.warn(`Failed to evaluate condition: ${condition}`, error);
      return false;
    }
  }

  /**
   * Add item to memory
   */
  private addToMemory(item: any): void {
    this.state.memory.push(item);
    
    // Limit memory size
    const maxMemorySize = this.config.memorySize || 1000;
    if (this.state.memory.length > maxMemorySize) {
      this.state.memory = this.state.memory.slice(-maxMemorySize);
    }
  }

  /**
   * Emit agent event
   */
  private emitEvent(type: string, data: any): void {
    const event: MCPAgentEvent = {
      type: type as any,
      agentId: this.state.id,
      timestamp: new Date(),
      data
    };

    this.emit('agentEvent', event);
  }

  /**
   * Get current agent state
   */
  public getState(): MCPAgentState {
    return { ...this.state };
  }

  /**
   * Stop the agent
   */
  public stop(): void {
    this.isRunning = false;
    this.state.status = 'idle';
    this.logger?.info(`Agent stopped: ${this.state.id}`);
  }

  /**
   * Get agent memory
   */
  public getMemory(): any[] {
    return [...this.state.memory];
  }

  /**
   * Clear agent memory
   */
  public clearMemory(): void {
    this.state.memory = [];
    this.logger?.debug('Agent memory cleared');
  }
}
