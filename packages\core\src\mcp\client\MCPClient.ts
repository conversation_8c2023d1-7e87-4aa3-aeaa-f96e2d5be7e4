/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import type { Logger } from '@inkbytefo/s647-shared';
import type {
  MCPClientConfig,
  MCPServerConfig,
  MCPServerInfo,
  MCPToolWrapper,
  MCPResourceWrapper,
  MCPPromptWrapper,
  MCPAgentContext,
  MCPConnectionError,
  MCPToolError,
  CallToolResult,
  ReadResourceResult,
  GetPromptResult
} from '../types/index.js';
import { EventEmitter } from 'events';

/**
 * MCP Client for managing connections to multiple MCP servers
 */
export class MCPClient extends EventEmitter {
  private config: MCPClientConfig;
  private logger?: Logger;
  private clients: Map<string, Client> = new Map();
  private serverInfo: Map<string, MCPServerInfo> = new Map();
  private tools: Map<string, MCPToolWrapper> = new Map();
  private resources: Map<string, MCPResourceWrapper> = new Map();
  private prompts: Map<string, MCPPromptWrapper> = new Map();
  private connectionPromises: Map<string, Promise<void>> = new Map();

  constructor(config: MCPClientConfig, logger?: Logger) {
    super();
    this.config = config;
    this.logger = logger;
  }

  /**
   * Initialize all configured MCP servers
   */
  public async initialize(): Promise<void> {
    this.logger?.info('Initializing MCP client with servers:', Object.keys(this.config.servers));

    const connectionPromises = Object.entries(this.config.servers)
      .filter(([_, serverConfig]) => serverConfig.enabled !== false)
      .map(([serverId, serverConfig]) => this.connectToServer(serverId, serverConfig));

    await Promise.allSettled(connectionPromises);
    
    this.logger?.info(`MCP client initialized. Connected to ${this.clients.size} servers.`);
  }

  /**
   * Connect to a specific MCP server
   */
  public async connectToServer(serverId: string, serverConfig: MCPServerConfig): Promise<void> {
    if (this.connectionPromises.has(serverId)) {
      return this.connectionPromises.get(serverId)!;
    }

    const connectionPromise = this._connectToServer(serverId, serverConfig);
    this.connectionPromises.set(serverId, connectionPromise);

    try {
      await connectionPromise;
    } finally {
      this.connectionPromises.delete(serverId);
    }
  }

  private async _connectToServer(serverId: string, serverConfig: MCPServerConfig): Promise<void> {
    try {
      this.logger?.debug(`Connecting to MCP server: ${serverId}`);

      // Create transport based on configuration
      let transport;
      if (serverConfig.transport === 'sse') {
        // For SSE transport, we need a URL
        const url = serverConfig.args?.[0] || 'http://localhost:3000/sse';
        transport = new SSEClientTransport(new URL(url));
      } else {
        // Default to stdio transport
        transport = new StdioClientTransport({
          command: serverConfig.command,
          args: serverConfig.args || [],
          env: serverConfig.env
        });
      }

      // Create and connect client
      const client = new Client({
        name: `arif-ai-${serverId}`,
        version: '2.0.0'
      }, {
        capabilities: {
          tools: {},
          resources: {},
          prompts: {}
        }
      });

      await client.connect(transport);
      this.clients.set(serverId, client);

      // Get server info
      const serverInfo = await this.getServerInfo(serverId, client);
      this.serverInfo.set(serverId, serverInfo);

      // Load tools, resources, and prompts
      await this.loadServerCapabilities(serverId, client);

      this.emit('serverConnected', serverId, serverInfo);
      this.logger?.info(`Connected to MCP server: ${serverId}`);

    } catch (error) {
      const mcpError = new MCPConnectionError(serverId, error as Error);
      this.serverInfo.set(serverId, {
        name: serverId,
        version: 'unknown',
        capabilities: {},
        status: 'error',
        lastError: mcpError.message
      });

      this.emit('serverError', serverId, mcpError);
      this.logger?.error(`Failed to connect to MCP server ${serverId}:`, error);
      throw mcpError;
    }
  }

  /**
   * Get server information
   */
  private async getServerInfo(serverId: string, client: Client): Promise<MCPServerInfo> {
    try {
      // Try to get server capabilities
      const [toolsResult, resourcesResult, promptsResult] = await Promise.allSettled([
        client.listTools(),
        client.listResources(),
        client.listPrompts()
      ]);

      return {
        name: serverId,
        version: '1.0.0', // Default version
        capabilities: {
          tools: toolsResult.status === 'fulfilled',
          resources: resourcesResult.status === 'fulfilled',
          prompts: promptsResult.status === 'fulfilled'
        },
        status: 'connected'
      };
    } catch (error) {
      return {
        name: serverId,
        version: 'unknown',
        capabilities: {},
        status: 'error',
        lastError: (error as Error).message
      };
    }
  }

  /**
   * Load server capabilities (tools, resources, prompts)
   */
  private async loadServerCapabilities(serverId: string, client: Client): Promise<void> {
    try {
      // Load tools
      const toolsResult = await client.listTools();
      if (toolsResult.tools) {
        for (const tool of toolsResult.tools) {
          const toolWrapper: MCPToolWrapper = {
            serverId,
            tool,
            execute: async (params: any) => {
              return await client.callTool({ name: tool.name, arguments: params });
            }
          };
          this.tools.set(`${serverId}.${tool.name}`, toolWrapper);
        }
      }

      // Load resources
      const resourcesResult = await client.listResources();
      if (resourcesResult.resources) {
        for (const resource of resourcesResult.resources) {
          const resourceWrapper: MCPResourceWrapper = {
            serverId,
            resource,
            read: async () => {
              return await client.readResource({ uri: resource.uri });
            }
          };
          this.resources.set(`${serverId}.${resource.name || resource.uri}`, resourceWrapper);
        }
      }

      // Load prompts
      const promptsResult = await client.listPrompts();
      if (promptsResult.prompts) {
        for (const prompt of promptsResult.prompts) {
          const promptWrapper: MCPPromptWrapper = {
            serverId,
            prompt,
            get: async (args?: any) => {
              return await client.getPrompt({ name: prompt.name, arguments: args });
            }
          };
          this.prompts.set(`${serverId}.${prompt.name}`, promptWrapper);
        }
      }

      this.logger?.debug(`Loaded capabilities for server ${serverId}:`, {
        tools: toolsResult.tools?.length || 0,
        resources: resourcesResult.resources?.length || 0,
        prompts: promptsResult.prompts?.length || 0
      });

    } catch (error) {
      this.logger?.warn(`Failed to load some capabilities for server ${serverId}:`, error);
    }
  }

  /**
   * Execute a tool from any connected server
   */
  public async executeTool(toolId: string, params: any): Promise<CallToolResult> {
    const tool = this.tools.get(toolId);
    if (!tool) {
      throw new MCPToolError(toolId, 'unknown');
    }

    try {
      this.emit('toolCalled', tool.serverId, toolId, params);
      const result = await tool.execute(params);
      this.emit('toolCompleted', tool.serverId, toolId, result);
      return result;
    } catch (error) {
      const mcpError = new MCPToolError(toolId, tool.serverId, error as Error);
      this.emit('toolError', tool.serverId, toolId, mcpError);
      throw mcpError;
    }
  }

  /**
   * Read a resource from any connected server
   */
  public async readResource(resourceId: string): Promise<ReadResourceResult> {
    const resource = this.resources.get(resourceId);
    if (!resource) {
      throw new Error(`Resource not found: ${resourceId}`);
    }

    return await resource.read();
  }

  /**
   * Get a prompt from any connected server
   */
  public async getPrompt(promptId: string, args?: any): Promise<GetPromptResult> {
    const prompt = this.prompts.get(promptId);
    if (!prompt) {
      throw new Error(`Prompt not found: ${promptId}`);
    }

    return await prompt.get(args);
  }

  /**
   * Get agent context with all available capabilities
   */
  public getAgentContext(): MCPAgentContext {
    return {
      servers: new Map(this.serverInfo),
      tools: new Map(this.tools),
      resources: new Map(this.resources),
      prompts: new Map(this.prompts)
    };
  }

  /**
   * Get all available tools
   */
  public getAvailableTools(): MCPToolWrapper[] {
    return Array.from(this.tools.values());
  }

  /**
   * Get all available resources
   */
  public getAvailableResources(): MCPResourceWrapper[] {
    return Array.from(this.resources.values());
  }

  /**
   * Get all available prompts
   */
  public getAvailablePrompts(): MCPPromptWrapper[] {
    return Array.from(this.prompts.values());
  }

  /**
   * Get server status
   */
  public getServerStatus(): Map<string, MCPServerInfo> {
    return new Map(this.serverInfo);
  }

  /**
   * Disconnect from all servers
   */
  public async disconnect(): Promise<void> {
    this.logger?.info('Disconnecting from all MCP servers');

    const disconnectPromises = Array.from(this.clients.entries()).map(async ([serverId, client]) => {
      try {
        await client.close();
        this.emit('serverDisconnected', serverId);
      } catch (error) {
        this.logger?.warn(`Error disconnecting from server ${serverId}:`, error);
      }
    });

    await Promise.allSettled(disconnectPromises);

    this.clients.clear();
    this.serverInfo.clear();
    this.tools.clear();
    this.resources.clear();
    this.prompts.clear();
    this.connectionPromises.clear();

    this.logger?.info('Disconnected from all MCP servers');
  }

  /**
   * Refresh capabilities for all servers
   */
  public async refreshCapabilities(): Promise<void> {
    this.logger?.info('Refreshing MCP server capabilities');

    const refreshPromises = Array.from(this.clients.entries()).map(async ([serverId, client]) => {
      try {
        // Clear existing capabilities for this server
        const serverTools = Array.from(this.tools.keys()).filter(key => key.startsWith(`${serverId}.`));
        const serverResources = Array.from(this.resources.keys()).filter(key => key.startsWith(`${serverId}.`));
        const serverPrompts = Array.from(this.prompts.keys()).filter(key => key.startsWith(`${serverId}.`));

        serverTools.forEach(key => this.tools.delete(key));
        serverResources.forEach(key => this.resources.delete(key));
        serverPrompts.forEach(key => this.prompts.delete(key));

        // Reload capabilities
        await this.loadServerCapabilities(serverId, client);
      } catch (error) {
        this.logger?.warn(`Failed to refresh capabilities for server ${serverId}:`, error);
      }
    });

    await Promise.allSettled(refreshPromises);
    this.logger?.info('MCP server capabilities refreshed');
  }
}
