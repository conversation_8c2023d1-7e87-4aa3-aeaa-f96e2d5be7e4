/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { EventEmitter } from 'events';
import type { Logger } from '@inkbytefo/s647-shared';
import { MetricsCollector } from './MetricsCollector.js';
import { PerformanceMonitor } from './PerformanceMonitor.js';

/**
 * Telemetry event
 */
export interface TelemetryEvent {
  id: string;
  type: string;
  source: string;
  timestamp: Date;
  data: Record<string, any>;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

/**
 * Telemetry manager configuration
 */
export interface TelemetryManagerConfig {
  enabled: boolean;
  anonymizeData: boolean;
  bufferSize: number;
  flushInterval: number;
  retentionPeriod: number;
  enabledSources: string[];
  excludedEvents: string[];
  samplingRate: number;
  endpoints: TelemetryEndpoint[];
}

/**
 * Telemetry endpoint
 */
export interface TelemetryEndpoint {
  name: string;
  url: string;
  headers?: Record<string, string>;
  enabled: boolean;
  batchSize: number;
  timeout: number;
}

/**
 * Usage analytics
 */
export interface UsageAnalytics {
  totalEvents: number;
  uniqueUsers: number;
  uniqueSessions: number;
  topEvents: Array<{ event: string; count: number }>;
  topSources: Array<{ source: string; count: number }>;
  timeRange: {
    start: Date;
    end: Date;
  };
}

/**
 * Telemetry manager for collecting and analyzing usage data
 */
export class TelemetryManager extends EventEmitter {
  private config: TelemetryManagerConfig;
  private logger?: Logger;
  private metricsCollector: MetricsCollector;
  private performanceMonitor: PerformanceMonitor;
  private events: TelemetryEvent[] = [];
  private flushTimer?: NodeJS.Timeout;
  private eventCounts: Map<string, number> = new Map();
  private sourceCounts: Map<string, number> = new Map();
  private userSessions: Set<string> = new Set();
  private uniqueUsers: Set<string> = new Set();

  constructor(
    metricsCollector: MetricsCollector,
    performanceMonitor: PerformanceMonitor,
    config: Partial<TelemetryManagerConfig> = {},
    logger?: Logger
  ) {
    super();
    this.metricsCollector = metricsCollector;
    this.performanceMonitor = performanceMonitor;
    this.logger = logger;
    this.config = {
      enabled: true,
      anonymizeData: true,
      bufferSize: 1000,
      flushInterval: 300000, // 5 minutes
      retentionPeriod: 2592000000, // 30 days
      enabledSources: ['*'], // All sources
      excludedEvents: [],
      samplingRate: 1.0, // 100% sampling
      endpoints: [],
      ...config
    };

    if (this.config.enabled) {
      this.startTelemetry();
    }
  }

  /**
   * Start telemetry collection
   */
  public startTelemetry(): void {
    if (!this.config.enabled) return;

    // Start flush timer
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);

    // Listen to system events
    this.setupEventListeners();

    this.logger?.info('Telemetry collection started');
  }

  /**
   * Stop telemetry collection
   */
  public stopTelemetry(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }

    this.flush(); // Final flush
    this.logger?.info('Telemetry collection stopped');
  }

  /**
   * Track an event
   */
  public track(
    type: string,
    source: string,
    data: Record<string, any> = {},
    userId?: string,
    sessionId?: string,
    metadata?: Record<string, any>
  ): void {
    if (!this.config.enabled) return;

    // Check if source is enabled
    if (!this.isSourceEnabled(source)) return;

    // Check if event is excluded
    if (this.config.excludedEvents.includes(type)) return;

    // Apply sampling
    if (Math.random() > this.config.samplingRate) return;

    const event: TelemetryEvent = {
      id: `event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      source,
      timestamp: new Date(),
      data: this.config.anonymizeData ? this.anonymizeData(data) : data,
      userId: this.config.anonymizeData ? this.anonymizeUserId(userId) : userId,
      sessionId,
      metadata
    };

    this.events.push(event);
    this.updateCounts(event);

    // Check buffer size
    if (this.events.length >= this.config.bufferSize) {
      this.flush();
    }

    this.emit('eventTracked', event);
  }

  /**
   * Track user action
   */
  public trackUserAction(
    action: string,
    component: string,
    userId?: string,
    sessionId?: string,
    properties?: Record<string, any>
  ): void {
    this.track('user_action', component, {
      action,
      ...properties
    }, userId, sessionId);
  }

  /**
   * Track feature usage
   */
  public trackFeatureUsage(
    feature: string,
    component: string,
    userId?: string,
    sessionId?: string,
    properties?: Record<string, any>
  ): void {
    this.track('feature_usage', component, {
      feature,
      ...properties
    }, userId, sessionId);
  }

  /**
   * Track error
   */
  public trackError(
    error: Error,
    component: string,
    userId?: string,
    sessionId?: string,
    context?: Record<string, any>
  ): void {
    this.track('error', component, {
      error: error.message,
      stack: error.stack,
      name: error.name,
      ...context
    }, userId, sessionId);
  }

  /**
   * Track performance
   */
  public trackPerformance(
    operation: string,
    component: string,
    duration: number,
    success: boolean,
    userId?: string,
    sessionId?: string,
    metadata?: Record<string, any>
  ): void {
    this.track('performance', component, {
      operation,
      duration,
      success,
      ...metadata
    }, userId, sessionId);

    // Also record in performance monitor
    this.performanceMonitor.recordPerformance({
      component,
      operation,
      duration,
      success,
      timestamp: new Date(),
      metadata
    });
  }

  /**
   * Track agent operation
   */
  public trackAgentOperation(
    agentId: string,
    operation: string,
    duration: number,
    success: boolean,
    tokensUsed?: number,
    cost?: number,
    userId?: string,
    sessionId?: string,
    metadata?: Record<string, any>
  ): void {
    this.track('agent_operation', 'agent', {
      agentId,
      operation,
      duration,
      success,
      tokensUsed,
      cost,
      ...metadata
    }, userId, sessionId);

    // Also record in performance monitor
    this.performanceMonitor.recordAgent({
      agentId,
      operation,
      duration,
      success,
      tokensUsed,
      cost,
      timestamp: new Date(),
      metadata
    });
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    // Listen to metrics collector events
    this.metricsCollector.on('flush', (data) => {
      this.track('metrics_flush', 'metrics_collector', data);
    });

    // Listen to performance monitor events
    this.performanceMonitor.on('alert', (alert) => {
      this.track('performance_alert', 'performance_monitor', {
        alertId: alert.id,
        severity: alert.threshold.severity,
        metric: alert.threshold.metric,
        value: alert.currentValue,
        threshold: alert.threshold.value
      });
    });

    this.performanceMonitor.on('report', (report) => {
      this.track('performance_report', 'performance_monitor', {
        reportId: report.id,
        totalOperations: report.summary.totalOperations,
        successRate: report.summary.successRate,
        averageResponseTime: report.summary.averageResponseTime,
        alertsCount: report.alerts.length
      });
    });
  }

  /**
   * Check if source is enabled
   */
  private isSourceEnabled(source: string): boolean {
    if (this.config.enabledSources.includes('*')) return true;
    return this.config.enabledSources.includes(source);
  }

  /**
   * Update event counts
   */
  private updateCounts(event: TelemetryEvent): void {
    // Update event counts
    const eventKey = `${event.source}.${event.type}`;
    this.eventCounts.set(eventKey, (this.eventCounts.get(eventKey) || 0) + 1);

    // Update source counts
    this.sourceCounts.set(event.source, (this.sourceCounts.get(event.source) || 0) + 1);

    // Track unique users and sessions
    if (event.userId) {
      this.uniqueUsers.add(event.userId);
    }
    if (event.sessionId) {
      this.userSessions.add(event.sessionId);
    }
  }

  /**
   * Anonymize data
   */
  private anonymizeData(data: Record<string, any>): Record<string, any> {
    const anonymized = { ...data };

    // Remove or hash sensitive fields
    const sensitiveFields = ['email', 'password', 'token', 'key', 'secret'];
    for (const field of sensitiveFields) {
      if (anonymized[field]) {
        anonymized[field] = this.hashValue(anonymized[field]);
      }
    }

    return anonymized;
  }

  /**
   * Anonymize user ID
   */
  private anonymizeUserId(userId?: string): string | undefined {
    if (!userId) return undefined;
    return this.hashValue(userId);
  }

  /**
   * Hash a value for anonymization
   */
  private hashValue(value: string): string {
    // Simple hash function - in production, use proper cryptographic hashing
    let hash = 0;
    for (let i = 0; i < value.length; i++) {
      const char = value.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Flush events to endpoints
   */
  private async flush(): Promise<void> {
    if (this.events.length === 0) return;

    const eventsToFlush = [...this.events];
    this.events = [];

    this.logger?.debug(`Flushing ${eventsToFlush.length} telemetry events`);

    // Send to configured endpoints
    for (const endpoint of this.config.endpoints) {
      if (!endpoint.enabled) continue;

      try {
        await this.sendToEndpoint(endpoint, eventsToFlush);
      } catch (error) {
        this.logger?.warn(`Failed to send telemetry to ${endpoint.name}:`, error);
      }
    }

    // Clean old data
    this.cleanOldData();

    this.emit('flush', {
      eventsCount: eventsToFlush.length,
      timestamp: new Date()
    });
  }

  /**
   * Send events to endpoint
   */
  private async sendToEndpoint(endpoint: TelemetryEndpoint, events: TelemetryEvent[]): Promise<void> {
    // Batch events
    const batches = this.batchEvents(events, endpoint.batchSize);

    for (const batch of batches) {
      const payload = {
        events: batch,
        timestamp: new Date().toISOString(),
        source: 'arif-ai'
      };

      // In a real implementation, you would send HTTP requests here
      this.logger?.debug(`Would send ${batch.length} events to ${endpoint.name}`);
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  /**
   * Batch events
   */
  private batchEvents(events: TelemetryEvent[], batchSize: number): TelemetryEvent[][] {
    const batches: TelemetryEvent[][] = [];
    for (let i = 0; i < events.length; i += batchSize) {
      batches.push(events.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Clean old data
   */
  private cleanOldData(): void {
    const cutoff = new Date(Date.now() - this.config.retentionPeriod);
    this.events = this.events.filter(e => e.timestamp >= cutoff);
  }

  /**
   * Get usage analytics
   */
  public getUsageAnalytics(timeRange?: { start: Date; end: Date }): UsageAnalytics {
    const now = new Date();
    const start = timeRange?.start || new Date(now.getTime() - 86400000); // 24 hours ago
    const end = timeRange?.end || now;

    const filteredEvents = this.events.filter(e => 
      e.timestamp >= start && e.timestamp <= end
    );

    // Calculate top events
    const eventCounts = new Map<string, number>();
    for (const event of filteredEvents) {
      const key = `${event.source}.${event.type}`;
      eventCounts.set(key, (eventCounts.get(key) || 0) + 1);
    }

    const topEvents = Array.from(eventCounts.entries())
      .map(([event, count]) => ({ event, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calculate top sources
    const sourceCounts = new Map<string, number>();
    for (const event of filteredEvents) {
      sourceCounts.set(event.source, (sourceCounts.get(event.source) || 0) + 1);
    }

    const topSources = Array.from(sourceCounts.entries())
      .map(([source, count]) => ({ source, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Count unique users and sessions in time range
    const uniqueUsersInRange = new Set(
      filteredEvents.map(e => e.userId).filter(Boolean)
    );
    const uniqueSessionsInRange = new Set(
      filteredEvents.map(e => e.sessionId).filter(Boolean)
    );

    return {
      totalEvents: filteredEvents.length,
      uniqueUsers: uniqueUsersInRange.size,
      uniqueSessions: uniqueSessionsInRange.size,
      topEvents,
      topSources,
      timeRange: { start, end }
    };
  }

  /**
   * Get events by type
   */
  public getEventsByType(type: string, limit: number = 100): TelemetryEvent[] {
    return this.events
      .filter(e => e.type === type)
      .slice(-limit);
  }

  /**
   * Get events by source
   */
  public getEventsBySource(source: string, limit: number = 100): TelemetryEvent[] {
    return this.events
      .filter(e => e.source === source)
      .slice(-limit);
  }

  /**
   * Get recent events
   */
  public getRecentEvents(limit: number = 100): TelemetryEvent[] {
    return this.events.slice(-limit);
  }

  /**
   * Add telemetry endpoint
   */
  public addEndpoint(endpoint: TelemetryEndpoint): void {
    this.config.endpoints.push(endpoint);
    this.logger?.debug(`Added telemetry endpoint: ${endpoint.name}`);
  }

  /**
   * Remove telemetry endpoint
   */
  public removeEndpoint(name: string): boolean {
    const index = this.config.endpoints.findIndex(e => e.name === name);
    if (index >= 0) {
      this.config.endpoints.splice(index, 1);
      this.logger?.debug(`Removed telemetry endpoint: ${name}`);
      return true;
    }
    return false;
  }

  /**
   * Get telemetry statistics
   */
  public getStatistics(): {
    totalEvents: number;
    uniqueUsers: number;
    uniqueSessions: number;
    topEvents: Array<{ event: string; count: number }>;
    topSources: Array<{ source: string; count: number }>;
    bufferSize: number;
    configuredEndpoints: number;
  } {
    const topEvents = Array.from(this.eventCounts.entries())
      .map(([event, count]) => ({ event, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const topSources = Array.from(this.sourceCounts.entries())
      .map(([source, count]) => ({ source, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalEvents: this.events.length,
      uniqueUsers: this.uniqueUsers.size,
      uniqueSessions: this.userSessions.size,
      topEvents,
      topSources,
      bufferSize: this.events.length,
      configuredEndpoints: this.config.endpoints.length
    };
  }
}
