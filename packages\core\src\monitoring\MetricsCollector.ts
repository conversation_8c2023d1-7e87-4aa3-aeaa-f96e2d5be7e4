/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { EventEmitter } from 'events';
import type { Logger } from '@inkbytefo/s647-shared';

/**
 * Metric types
 */
export enum MetricType {
  COUNTER = 'counter',
  GAUGE = 'gauge',
  HISTOGRAM = 'histogram',
  TIMER = 'timer'
}

/**
 * Metric data point
 */
export interface MetricDataPoint {
  name: string;
  type: MetricType;
  value: number;
  timestamp: Date;
  tags?: Record<string, string>;
  metadata?: Record<string, any>;
}

/**
 * Aggregated metric
 */
export interface AggregatedMetric {
  name: string;
  type: MetricType;
  count: number;
  sum: number;
  min: number;
  max: number;
  avg: number;
  p50?: number;
  p95?: number;
  p99?: number;
  tags?: Record<string, string>;
  timeWindow: {
    start: Date;
    end: Date;
  };
}

/**
 * Performance metric
 */
export interface PerformanceMetric {
  component: string;
  operation: string;
  duration: number;
  success: boolean;
  error?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * System metric
 */
export interface SystemMetric {
  cpu: number;
  memory: number;
  disk: number;
  network: {
    bytesIn: number;
    bytesOut: number;
  };
  timestamp: Date;
}

/**
 * Agent metric
 */
export interface AgentMetric {
  agentId: string;
  operation: string;
  duration: number;
  success: boolean;
  tokensUsed?: number;
  cost?: number;
  error?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Metrics collector configuration
 */
export interface MetricsConfig {
  enabled: boolean;
  bufferSize: number;
  flushInterval: number;
  retentionPeriod: number;
  aggregationWindows: number[];
  enableSystemMetrics: boolean;
  enablePerformanceMetrics: boolean;
  enableAgentMetrics: boolean;
}

/**
 * Comprehensive metrics collector for monitoring system performance
 */
export class MetricsCollector extends EventEmitter {
  private config: MetricsConfig;
  private logger?: Logger;
  private metrics: MetricDataPoint[] = [];
  private performanceMetrics: PerformanceMetric[] = [];
  private systemMetrics: SystemMetric[] = [];
  private agentMetrics: AgentMetric[] = [];
  private aggregatedMetrics: Map<string, AggregatedMetric> = new Map();
  private timers: Map<string, number> = new Map();
  private counters: Map<string, number> = new Map();
  private gauges: Map<string, number> = new Map();
  private flushTimer?: NodeJS.Timeout;
  private systemMetricsTimer?: NodeJS.Timeout;

  constructor(config: Partial<MetricsConfig> = {}, logger?: Logger) {
    super();
    this.config = {
      enabled: true,
      bufferSize: 10000,
      flushInterval: 60000, // 1 minute
      retentionPeriod: 86400000, // 24 hours
      aggregationWindows: [60000, 300000, 3600000], // 1min, 5min, 1hour
      enableSystemMetrics: true,
      enablePerformanceMetrics: true,
      enableAgentMetrics: true,
      ...config
    };
    this.logger = logger;

    if (this.config.enabled) {
      this.startCollection();
    }
  }

  /**
   * Start metrics collection
   */
  public startCollection(): void {
    if (!this.config.enabled) return;

    // Start flush timer
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);

    // Start system metrics collection
    if (this.config.enableSystemMetrics) {
      this.systemMetricsTimer = setInterval(() => {
        this.collectSystemMetrics();
      }, 30000); // Every 30 seconds
    }

    this.logger?.info('Metrics collection started');
  }

  /**
   * Stop metrics collection
   */
  public stopCollection(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }

    if (this.systemMetricsTimer) {
      clearInterval(this.systemMetricsTimer);
      this.systemMetricsTimer = undefined;
    }

    this.flush(); // Final flush
    this.logger?.info('Metrics collection stopped');
  }

  /**
   * Record a counter metric
   */
  public counter(name: string, value: number = 1, tags?: Record<string, string>): void {
    if (!this.config.enabled) return;

    const key = this.getMetricKey(name, tags);
    this.counters.set(key, (this.counters.get(key) || 0) + value);

    this.addMetric({
      name,
      type: MetricType.COUNTER,
      value,
      timestamp: new Date(),
      tags
    });
  }

  /**
   * Record a gauge metric
   */
  public gauge(name: string, value: number, tags?: Record<string, string>): void {
    if (!this.config.enabled) return;

    const key = this.getMetricKey(name, tags);
    this.gauges.set(key, value);

    this.addMetric({
      name,
      type: MetricType.GAUGE,
      value,
      timestamp: new Date(),
      tags
    });
  }

  /**
   * Start a timer
   */
  public startTimer(name: string, tags?: Record<string, string>): string {
    const timerId = `${name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    this.timers.set(timerId, Date.now());
    return timerId;
  }

  /**
   * End a timer and record the duration
   */
  public endTimer(timerId: string, name?: string, tags?: Record<string, string>): number {
    const startTime = this.timers.get(timerId);
    if (!startTime) {
      this.logger?.warn(`Timer not found: ${timerId}`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(timerId);

    if (this.config.enabled) {
      this.addMetric({
        name: name || 'timer',
        type: MetricType.TIMER,
        value: duration,
        timestamp: new Date(),
        tags
      });
    }

    return duration;
  }

  /**
   * Record a histogram value
   */
  public histogram(name: string, value: number, tags?: Record<string, string>): void {
    if (!this.config.enabled) return;

    this.addMetric({
      name,
      type: MetricType.HISTOGRAM,
      value,
      timestamp: new Date(),
      tags
    });
  }

  /**
   * Record performance metric
   */
  public recordPerformance(metric: PerformanceMetric): void {
    if (!this.config.enabled || !this.config.enablePerformanceMetrics) return;

    this.performanceMetrics.push(metric);
    this.counter('performance.operations.total', 1, {
      component: metric.component,
      operation: metric.operation,
      success: metric.success.toString()
    });

    this.histogram('performance.duration', metric.duration, {
      component: metric.component,
      operation: metric.operation
    });

    if (!metric.success) {
      this.counter('performance.errors.total', 1, {
        component: metric.component,
        operation: metric.operation
      });
    }

    this.emit('performanceMetric', metric);
  }

  /**
   * Record agent metric
   */
  public recordAgent(metric: AgentMetric): void {
    if (!this.config.enabled || !this.config.enableAgentMetrics) return;

    this.agentMetrics.push(metric);
    this.counter('agent.operations.total', 1, {
      agentId: metric.agentId,
      operation: metric.operation,
      success: metric.success.toString()
    });

    this.histogram('agent.duration', metric.duration, {
      agentId: metric.agentId,
      operation: metric.operation
    });

    if (metric.tokensUsed) {
      this.histogram('agent.tokens', metric.tokensUsed, {
        agentId: metric.agentId,
        operation: metric.operation
      });
    }

    if (metric.cost) {
      this.histogram('agent.cost', metric.cost, {
        agentId: metric.agentId,
        operation: metric.operation
      });
    }

    if (!metric.success) {
      this.counter('agent.errors.total', 1, {
        agentId: metric.agentId,
        operation: metric.operation
      });
    }

    this.emit('agentMetric', metric);
  }

  /**
   * Collect system metrics
   */
  private async collectSystemMetrics(): Promise<void> {
    if (!this.config.enableSystemMetrics) return;

    try {
      const metric: SystemMetric = {
        cpu: await this.getCpuUsage(),
        memory: await this.getMemoryUsage(),
        disk: await this.getDiskUsage(),
        network: await this.getNetworkUsage(),
        timestamp: new Date()
      };

      this.systemMetrics.push(metric);

      // Record as individual metrics
      this.gauge('system.cpu', metric.cpu);
      this.gauge('system.memory', metric.memory);
      this.gauge('system.disk', metric.disk);
      this.gauge('system.network.bytes_in', metric.network.bytesIn);
      this.gauge('system.network.bytes_out', metric.network.bytesOut);

      this.emit('systemMetric', metric);

    } catch (error) {
      this.logger?.warn('Failed to collect system metrics:', error);
    }
  }

  /**
   * Add metric to buffer
   */
  private addMetric(metric: MetricDataPoint): void {
    this.metrics.push(metric);

    // Check buffer size
    if (this.metrics.length >= this.config.bufferSize) {
      this.flush();
    }
  }

  /**
   * Flush metrics and perform aggregation
   */
  private flush(): void {
    if (this.metrics.length === 0) return;

    this.logger?.debug(`Flushing ${this.metrics.length} metrics`);

    // Perform aggregation
    this.aggregateMetrics();

    // Clean old data
    this.cleanOldData();

    // Emit flush event
    this.emit('flush', {
      metricsCount: this.metrics.length,
      timestamp: new Date()
    });

    // Clear processed metrics (in production, you might want to send them to external storage)
    this.metrics = [];
  }

  /**
   * Aggregate metrics for different time windows
   */
  private aggregateMetrics(): void {
    const now = new Date();

    for (const windowMs of this.config.aggregationWindows) {
      const windowStart = new Date(now.getTime() - windowMs);
      
      // Group metrics by name and tags
      const groups = new Map<string, MetricDataPoint[]>();
      
      for (const metric of this.metrics) {
        if (metric.timestamp >= windowStart) {
          const key = this.getMetricKey(metric.name, metric.tags);
          if (!groups.has(key)) {
            groups.set(key, []);
          }
          groups.get(key)!.push(metric);
        }
      }

      // Calculate aggregations
      for (const [key, groupMetrics] of groups) {
        if (groupMetrics.length === 0) continue;

        const values = groupMetrics.map(m => m.value);
        const aggregated: AggregatedMetric = {
          name: groupMetrics[0].name,
          type: groupMetrics[0].type,
          count: values.length,
          sum: values.reduce((a, b) => a + b, 0),
          min: Math.min(...values),
          max: Math.max(...values),
          avg: values.reduce((a, b) => a + b, 0) / values.length,
          tags: groupMetrics[0].tags,
          timeWindow: {
            start: windowStart,
            end: now
          }
        };

        // Calculate percentiles for histograms and timers
        if (groupMetrics[0].type === MetricType.HISTOGRAM || groupMetrics[0].type === MetricType.TIMER) {
          const sorted = values.sort((a, b) => a - b);
          aggregated.p50 = this.percentile(sorted, 0.5);
          aggregated.p95 = this.percentile(sorted, 0.95);
          aggregated.p99 = this.percentile(sorted, 0.99);
        }

        const aggregatedKey = `${key}-${windowMs}`;
        this.aggregatedMetrics.set(aggregatedKey, aggregated);
      }
    }
  }

  /**
   * Calculate percentile
   */
  private percentile(sortedValues: number[], p: number): number {
    const index = Math.ceil(sortedValues.length * p) - 1;
    return sortedValues[Math.max(0, index)];
  }

  /**
   * Clean old data
   */
  private cleanOldData(): void {
    const cutoff = new Date(Date.now() - this.config.retentionPeriod);

    // Clean performance metrics
    this.performanceMetrics = this.performanceMetrics.filter(m => m.timestamp >= cutoff);

    // Clean system metrics
    this.systemMetrics = this.systemMetrics.filter(m => m.timestamp >= cutoff);

    // Clean agent metrics
    this.agentMetrics = this.agentMetrics.filter(m => m.timestamp >= cutoff);

    // Clean aggregated metrics
    for (const [key, metric] of this.aggregatedMetrics) {
      if (metric.timeWindow.end < cutoff) {
        this.aggregatedMetrics.delete(key);
      }
    }
  }

  /**
   * Get metric key for grouping
   */
  private getMetricKey(name: string, tags?: Record<string, string>): string {
    if (!tags || Object.keys(tags).length === 0) {
      return name;
    }

    const tagString = Object.entries(tags)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([k, v]) => `${k}=${v}`)
      .join(',');

    return `${name}{${tagString}}`;
  }

  /**
   * System metrics collection methods
   */
  private async getCpuUsage(): Promise<number> {
    // Simplified CPU usage calculation
    // In production, use proper system monitoring libraries
    return Math.random() * 100;
  }

  private async getMemoryUsage(): Promise<number> {
    const used = process.memoryUsage();
    const total = used.heapTotal + used.external;
    return (used.heapUsed / total) * 100;
  }

  private async getDiskUsage(): Promise<number> {
    // Simplified disk usage
    // In production, use proper disk monitoring
    return Math.random() * 100;
  }

  private async getNetworkUsage(): Promise<{ bytesIn: number; bytesOut: number }> {
    // Simplified network usage
    // In production, use proper network monitoring
    return {
      bytesIn: Math.floor(Math.random() * 1000000),
      bytesOut: Math.floor(Math.random() * 1000000)
    };
  }

  /**
   * Get current metrics snapshot
   */
  public getMetricsSnapshot(): {
    counters: Record<string, number>;
    gauges: Record<string, number>;
    recentMetrics: MetricDataPoint[];
    aggregatedMetrics: AggregatedMetric[];
    performanceMetrics: PerformanceMetric[];
    systemMetrics: SystemMetric[];
    agentMetrics: AgentMetric[];
  } {
    return {
      counters: Object.fromEntries(this.counters),
      gauges: Object.fromEntries(this.gauges),
      recentMetrics: [...this.metrics],
      aggregatedMetrics: Array.from(this.aggregatedMetrics.values()),
      performanceMetrics: [...this.performanceMetrics],
      systemMetrics: [...this.systemMetrics],
      agentMetrics: [...this.agentMetrics]
    };
  }

  /**
   * Get metrics for a specific time range
   */
  public getMetricsInRange(start: Date, end: Date): {
    metrics: MetricDataPoint[];
    performanceMetrics: PerformanceMetric[];
    systemMetrics: SystemMetric[];
    agentMetrics: AgentMetric[];
  } {
    return {
      metrics: this.metrics.filter(m => m.timestamp >= start && m.timestamp <= end),
      performanceMetrics: this.performanceMetrics.filter(m => m.timestamp >= start && m.timestamp <= end),
      systemMetrics: this.systemMetrics.filter(m => m.timestamp >= start && m.timestamp <= end),
      agentMetrics: this.agentMetrics.filter(m => m.timestamp >= start && m.timestamp <= end)
    };
  }

  /**
   * Get aggregated metrics for a specific window
   */
  public getAggregatedMetrics(windowMs: number): AggregatedMetric[] {
    return Array.from(this.aggregatedMetrics.values())
      .filter(m => m.timeWindow.end.getTime() - m.timeWindow.start.getTime() === windowMs);
  }

  /**
   * Reset all metrics
   */
  public reset(): void {
    this.metrics = [];
    this.performanceMetrics = [];
    this.systemMetrics = [];
    this.agentMetrics = [];
    this.aggregatedMetrics.clear();
    this.counters.clear();
    this.gauges.clear();
    this.timers.clear();
    this.logger?.info('Metrics reset');
  }
}
