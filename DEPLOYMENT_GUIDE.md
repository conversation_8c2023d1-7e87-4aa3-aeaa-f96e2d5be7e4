# Arif AI Agentic System - Deployment Guide

## 🎯 Overview

This guide covers the deployment of Arif AI's enhanced agentic system for alpha testing and production environments. The system includes autonomous agents, MCP integration, security safeguards, and performance monitoring.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CLI Client    │    │   Web Interface │    │   API Gateway   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │     Core System         │
                    │  ┌─────────────────┐    │
                    │  │ Agent Manager   │    │
                    │  ├─────────────────┤    │
                    │  │ MCP Client      │    │
                    │  ├─────────────────┤    │
                    │  │ Tool Orchestr.  │    │
                    │  ├─────────────────┤    │
                    │  │ Security Mgr    │    │
                    │  ├─────────────────┤    │
                    │  │ Performance Mon │    │
                    │  └─────────────────┘    │
                    └─────────────┬───────────┘
                                  │
                    ┌─────────────┴───────────┐
                    │    External Services    │
                    │  ┌─────────────────┐    │
                    │  │ AI Providers    │    │
                    │  ├─────────────────┤    │
                    │  │ MCP Servers     │    │
                    │  ├─────────────────┤    │
                    │  │ Databases       │    │
                    │  ├─────────────────┤    │
                    │  │ Monitoring      │    │
                    │  └─────────────────┘    │
                    └─────────────────────────┘
```

## 🔧 Prerequisites

### System Requirements

#### Minimum Requirements
- **CPU**: 2 cores, 2.0GHz
- **RAM**: 4GB
- **Storage**: 20GB available space
- **Network**: Stable internet connection

#### Recommended Requirements
- **CPU**: 4+ cores, 2.5GHz+
- **RAM**: 8GB+
- **Storage**: 50GB+ SSD
- **Network**: High-speed internet connection

### Software Dependencies

#### Required
- **Node.js**: 18.0.0 or higher
- **npm**: 9.0.0 or higher
- **Git**: 2.30.0 or higher

#### Optional
- **Docker**: 20.10.0+ (for containerized deployment)
- **Docker Compose**: 2.0.0+ (for multi-service deployment)
- **PM2**: 5.0.0+ (for process management)

### Environment Setup

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y  # Ubuntu/Debian
# or
brew update && brew upgrade             # macOS

# Install Node.js (using nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18

# Install global dependencies
npm install -g pm2 typescript ts-node

# Verify installations
node --version    # Should be 18.x.x
npm --version     # Should be 9.x.x
git --version     # Should be 2.30.x+
```

## 📦 Installation

### 1. Clone Repository

```bash
# Clone the repository
git clone https://github.com/inkbytefo/s647.git
cd s647

# Checkout alpha branch (if applicable)
git checkout alpha

# Verify repository structure
ls -la
```

### 2. Install Dependencies

```bash
# Install all dependencies
npm install

# Install peer dependencies
npm install --peer

# Verify installation
npm list --depth=0
```

### 3. Build Project

```bash
# Build all packages
npm run build

# Verify build
ls -la packages/*/dist/

# Run post-build verification
npm run verify
```

### 4. Configuration

#### Environment Variables

Create `.env` file in the project root:

```bash
# Core Configuration
NODE_ENV=production
LOG_LEVEL=info
DEBUG=arif:*

# AI Provider Configuration
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here

# Security Configuration
SECURITY_ENABLED=true
SECURITY_STRICT_MODE=false
RATE_LIMITING_ENABLED=true
JWT_SECRET=your_jwt_secret_here

# Database Configuration (if applicable)
DATABASE_URL=postgresql://user:password@localhost:5432/arif_ai
REDIS_URL=redis://localhost:6379

# Monitoring Configuration
METRICS_ENABLED=true
TELEMETRY_ENABLED=false
PERFORMANCE_MONITORING=true
SENTRY_DSN=your_sentry_dsn_here

# MCP Configuration
MCP_ENABLED=true
MCP_SERVER_PORT=3001
MCP_CLIENT_TIMEOUT=30000

# External Services
WEBHOOK_URL=https://your-webhook-endpoint.com
NOTIFICATION_EMAIL=<EMAIL>
```

#### Configuration Files

Create `config/production.json`:

```json
{
  "server": {
    "port": 3000,
    "host": "0.0.0.0",
    "cors": {
      "enabled": true,
      "origins": ["https://yourdomain.com"]
    }
  },
  "agents": {
    "enabled": true,
    "maxConcurrentExecutions": 10,
    "defaultTimeout": 300000,
    "enableMemory": true,
    "memorySize": 1000
  },
  "mcp": {
    "enabled": true,
    "defaultTimeout": 30000,
    "maxRetries": 3,
    "servers": {
      "local-tools": {
        "name": "Local Tools Server",
        "command": "node",
        "args": ["dist/mcp-server.js"],
        "transport": "stdio",
        "enabled": true,
        "timeout": 30000,
        "retries": 3
      }
    }
  },
  "security": {
    "enabled": true,
    "strictMode": false,
    "rateLimiting": {
      "enabled": true,
      "global": {
        "windowMs": 60000,
        "maxRequests": 1000
      },
      "perUser": {
        "windowMs": 60000,
        "maxRequests": 100
      }
    },
    "inputValidation": {
      "enabled": true,
      "maxInputLength": 100000
    }
  },
  "monitoring": {
    "metrics": {
      "enabled": true,
      "bufferSize": 10000,
      "flushInterval": 60000
    },
    "performance": {
      "enabled": true,
      "alertingEnabled": true,
      "reportInterval": 300000
    },
    "telemetry": {
      "enabled": false,
      "anonymizeData": true,
      "samplingRate": 1.0
    }
  },
  "logging": {
    "level": "info",
    "format": "json",
    "destinations": ["console", "file"],
    "file": {
      "path": "logs/arif-ai.log",
      "maxSize": "100MB",
      "maxFiles": 10
    }
  }
}
```

## 🚀 Deployment Options

### Option 1: Direct Node.js Deployment

#### 1. Start Application

```bash
# Start with PM2 (recommended)
pm2 start ecosystem.config.js

# Or start directly
npm start

# Verify application is running
pm2 status
curl http://localhost:3000/health
```

#### 2. PM2 Configuration

Create `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [
    {
      name: 'arif-ai-core',
      script: 'dist/index.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      log_file: 'logs/combined.log',
      out_file: 'logs/out.log',
      error_file: 'logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=4096'
    },
    {
      name: 'arif-ai-mcp-server',
      script: 'dist/mcp-server.js',
      instances: 1,
      env: {
        NODE_ENV: 'production',
        MCP_PORT: 3001
      },
      log_file: 'logs/mcp-combined.log',
      out_file: 'logs/mcp-out.log',
      error_file: 'logs/mcp-error.log'
    }
  ]
};
```

### Option 2: Docker Deployment

#### 1. Create Dockerfile

```dockerfile
# Multi-stage build
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
COPY packages/*/package*.json ./packages/*/
RUN npm ci --only=production

COPY . .
RUN npm run build

# Production image
FROM node:18-alpine AS production

RUN addgroup -g 1001 -S nodejs
RUN adduser -S arif -u 1001

WORKDIR /app

# Copy built application
COPY --from=builder --chown=arif:nodejs /app/dist ./dist
COPY --from=builder --chown=arif:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=arif:nodejs /app/package*.json ./
COPY --from=builder --chown=arif:nodejs /app/config ./config

# Create logs directory
RUN mkdir -p logs && chown arif:nodejs logs

USER arif

EXPOSE 3000 3001

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

CMD ["npm", "start"]
```

#### 2. Create Docker Compose

```yaml
version: '3.8'

services:
  arif-ai:
    build: .
    ports:
      - "3000:3000"
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis
      - postgres

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 3

  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=arif_ai
      - POSTGRES_USER=arif
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U arif -d arif_ai"]
      interval: 30s
      timeout: 5s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - arif-ai
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
```

#### 3. Deploy with Docker

```bash
# Build and start services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f arif-ai

# Scale services (if needed)
docker-compose up -d --scale arif-ai=3
```

### Option 3: Kubernetes Deployment

#### 1. Create Kubernetes Manifests

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: arif-ai
  labels:
    app: arif-ai
spec:
  replicas: 3
  selector:
    matchLabels:
      app: arif-ai
  template:
    metadata:
      labels:
        app: arif-ai
    spec:
      containers:
      - name: arif-ai
        image: arif-ai:latest
        ports:
        - containerPort: 3000
        - containerPort: 3001
        env:
        - name: NODE_ENV
          value: "production"
        - name: LOG_LEVEL
          value: "info"
        envFrom:
        - secretRef:
            name: arif-ai-secrets
        - configMapRef:
            name: arif-ai-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: arif-ai-service
spec:
  selector:
    app: arif-ai
  ports:
  - name: http
    port: 80
    targetPort: 3000
  - name: mcp
    port: 3001
    targetPort: 3001
  type: LoadBalancer
```

#### 2. Deploy to Kubernetes

```bash
# Create namespace
kubectl create namespace arif-ai

# Create secrets
kubectl create secret generic arif-ai-secrets \
  --from-env-file=.env \
  --namespace=arif-ai

# Create config map
kubectl create configmap arif-ai-config \
  --from-file=config/ \
  --namespace=arif-ai

# Deploy application
kubectl apply -f k8s/ --namespace=arif-ai

# Check deployment status
kubectl get pods -n arif-ai
kubectl get services -n arif-ai
```

## 🔍 Monitoring and Logging

### Health Checks

```bash
# Basic health check
curl http://localhost:3000/health

# Detailed health check
curl http://localhost:3000/health/detailed

# Readiness check
curl http://localhost:3000/ready
```

### Logging Configuration

```bash
# Create logs directory
mkdir -p logs

# Set proper permissions
chmod 755 logs

# Configure log rotation (logrotate)
sudo tee /etc/logrotate.d/arif-ai << EOF
/path/to/arif-ai/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 arif arif
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
```

### Monitoring Setup

```bash
# Install monitoring tools
npm install -g @pm2/io

# Start with monitoring
pm2 start ecosystem.config.js --attach

# Monitor with PM2
pm2 monit

# View metrics
curl http://localhost:3000/metrics
```

## 🔒 Security Considerations

### SSL/TLS Configuration

```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Firewall Configuration

```bash
# Configure UFW (Ubuntu)
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 3000/tcp  # Block direct access to app
sudo ufw deny 3001/tcp  # Block direct access to MCP
```

### Environment Security

```bash
# Set proper file permissions
chmod 600 .env
chmod 600 config/production.json
chmod 700 logs/

# Create dedicated user
sudo useradd -r -s /bin/false arif
sudo chown -R arif:arif /path/to/arif-ai
```

## 🧪 Testing Deployment

### Smoke Tests

```bash
# Test basic functionality
curl -X POST http://localhost:3000/api/execute \
  -H "Content-Type: application/json" \
  -d '{"goal": "Test deployment", "agent": "general"}'

# Test MCP connectivity
curl http://localhost:3001/mcp/health

# Test agent listing
curl http://localhost:3000/api/agents

# Test metrics endpoint
curl http://localhost:3000/metrics
```

### Load Testing

```bash
# Install load testing tool
npm install -g artillery

# Create load test configuration
cat > load-test.yml << EOF
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "Basic agent execution"
    requests:
      - post:
          url: "/api/execute"
          json:
            goal: "Simple test task"
            agent: "general"
EOF

# Run load test
artillery run load-test.yml
```

## 🔄 Maintenance

### Updates and Upgrades

```bash
# Backup current deployment
tar -czf backup-$(date +%Y%m%d).tar.gz .

# Pull latest changes
git pull origin main

# Install new dependencies
npm install

# Rebuild application
npm run build

# Restart services
pm2 restart all

# Verify deployment
npm run test:smoke
```

### Database Maintenance

```bash
# Backup database
pg_dump arif_ai > backup-$(date +%Y%m%d).sql

# Run migrations (if applicable)
npm run migrate

# Optimize database
psql arif_ai -c "VACUUM ANALYZE;"
```

### Log Cleanup

```bash
# Clean old logs
find logs/ -name "*.log" -mtime +30 -delete

# Compress old logs
find logs/ -name "*.log" -mtime +7 -exec gzip {} \;

# Clean PM2 logs
pm2 flush
```

## 🚨 Troubleshooting

### Common Issues

1. **Application won't start**
   ```bash
   # Check logs
   pm2 logs arif-ai-core
   
   # Check configuration
   npm run config:validate
   
   # Check dependencies
   npm audit
   ```

2. **High memory usage**
   ```bash
   # Monitor memory
   pm2 monit
   
   # Restart application
   pm2 restart arif-ai-core
   
   # Check for memory leaks
   node --inspect dist/index.js
   ```

3. **MCP connection issues**
   ```bash
   # Check MCP server status
   pm2 status arif-ai-mcp-server
   
   # Test MCP connectivity
   curl http://localhost:3001/health
   
   # Check MCP logs
   pm2 logs arif-ai-mcp-server
   ```

### Emergency Procedures

```bash
# Emergency stop
pm2 stop all

# Emergency restart
pm2 restart all --update-env

# Rollback to previous version
git checkout HEAD~1
npm run build
pm2 restart all
```

## 📞 Support

### Getting Help
- **Documentation**: Check the [Agentic System Guide](AGENTIC_SYSTEM_GUIDE.md)
- **Issues**: Report issues on GitHub
- **Community**: Join our Discord server
- **Email**: <EMAIL>

### Emergency Contacts
- **Technical Lead**: <EMAIL>
- **DevOps Team**: <EMAIL>
- **On-call**: +1-555-EMERGENCY

---

**Note**: This deployment guide is for alpha testing. Production deployment may require additional security measures and optimizations.
