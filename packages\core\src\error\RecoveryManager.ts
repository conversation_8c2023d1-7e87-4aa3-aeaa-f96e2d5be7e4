/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { EventEmitter } from 'events';
import type { Logger } from '@inkbytefo/s647-shared';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, type EnhancedError, type ErrorContext } from './ErrorHandler.js';

/**
 * Recovery plan step
 */
export interface RecoveryStep {
  id: string;
  name: string;
  description: string;
  type: 'automatic' | 'manual' | 'conditional';
  action: RecoveryAction;
  condition?: string;
  timeout?: number;
  retries?: number;
  dependencies?: string[];
}

/**
 * Recovery action types
 */
export type RecoveryAction = 
  | { type: 'restart_service'; service: string }
  | { type: 'clear_cache'; cache: string }
  | { type: 'reset_connection'; connection: string }
  | { type: 'switch_provider'; provider: string }
  | { type: 'scale_resources'; factor: number }
  | { type: 'notify_admin'; message: string }
  | { type: 'execute_script'; script: string; args?: string[] }
  | { type: 'custom'; handler: string; params?: any };

/**
 * Recovery plan
 */
export interface RecoveryPlan {
  id: string;
  name: string;
  description: string;
  errorPattern: string;
  steps: RecoveryStep[];
  priority: number;
  enabled: boolean;
  lastUsed?: Date;
  successRate: number;
  totalExecutions: number;
}

/**
 * Recovery execution result
 */
export interface RecoveryExecutionResult {
  planId: string;
  stepResults: StepExecutionResult[];
  success: boolean;
  duration: number;
  error?: string;
  timestamp: Date;
}

/**
 * Step execution result
 */
export interface StepExecutionResult {
  stepId: string;
  success: boolean;
  duration: number;
  error?: string;
  output?: any;
}

/**
 * Recovery manager for coordinating complex recovery operations
 */
export class RecoveryManager extends EventEmitter {
  private errorHandler: ErrorHandler;
  private logger?: Logger;
  private recoveryPlans: Map<string, RecoveryPlan> = new Map();
  private customHandlers: Map<string, RecoveryHandler> = new Map();
  private activeRecoveries: Map<string, Promise<RecoveryExecutionResult>> = new Map();
  private executionHistory: RecoveryExecutionResult[] = [];

  constructor(errorHandler: ErrorHandler, logger?: Logger) {
    super();
    this.errorHandler = errorHandler;
    this.logger = logger;
    this.setupDefaultPlans();
  }

  /**
   * Register a recovery plan
   */
  public registerRecoveryPlan(plan: RecoveryPlan): void {
    this.recoveryPlans.set(plan.id, plan);
    this.logger?.debug(`Registered recovery plan: ${plan.id}`);
  }

  /**
   * Register a custom recovery handler
   */
  public registerCustomHandler(name: string, handler: RecoveryHandler): void {
    this.customHandlers.set(name, handler);
    this.logger?.debug(`Registered custom recovery handler: ${name}`);
  }

  /**
   * Execute recovery for an error
   */
  public async executeRecovery(error: EnhancedError): Promise<RecoveryExecutionResult> {
    const plan = this.findBestRecoveryPlan(error);
    if (!plan) {
      throw new Error(`No recovery plan found for error: ${error.id}`);
    }

    return await this.executePlan(plan, error);
  }

  /**
   * Execute a specific recovery plan
   */
  public async executePlan(plan: RecoveryPlan, error: EnhancedError): Promise<RecoveryExecutionResult> {
    const executionId = `recovery-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    if (this.activeRecoveries.has(plan.id)) {
      this.logger?.warn(`Recovery plan ${plan.id} is already executing`);
      return await this.activeRecoveries.get(plan.id)!;
    }

    const executionPromise = this._executePlan(plan, error, executionId);
    this.activeRecoveries.set(plan.id, executionPromise);

    try {
      const result = await executionPromise;
      this.updatePlanStatistics(plan, result.success);
      this.executionHistory.push(result);
      return result;
    } finally {
      this.activeRecoveries.delete(plan.id);
    }
  }

  /**
   * Internal plan execution
   */
  private async _executePlan(
    plan: RecoveryPlan,
    error: EnhancedError,
    executionId: string
  ): Promise<RecoveryExecutionResult> {
    const startTime = Date.now();
    const stepResults: StepExecutionResult[] = [];

    this.emit('recoveryStarted', plan.id, error.id);
    this.logger?.info(`Starting recovery plan: ${plan.id} for error: ${error.id}`);

    try {
      // Execute steps in order, respecting dependencies
      const executedSteps = new Set<string>();
      
      for (const step of plan.steps) {
        // Check dependencies
        if (step.dependencies && !this.areDependenciesMet(step.dependencies, executedSteps)) {
          continue;
        }

        // Check condition if specified
        if (step.condition && !this.evaluateCondition(step.condition, error)) {
          stepResults.push({
            stepId: step.id,
            success: true,
            duration: 0,
            output: 'skipped'
          });
          continue;
        }

        const stepResult = await this.executeStep(step, error);
        stepResults.push(stepResult);
        
        if (stepResult.success) {
          executedSteps.add(step.id);
        } else if (step.type === 'automatic') {
          // If automatic step fails, stop execution
          break;
        }
      }

      const success = stepResults.every(r => r.success);
      const result: RecoveryExecutionResult = {
        planId: plan.id,
        stepResults,
        success,
        duration: Date.now() - startTime,
        timestamp: new Date()
      };

      if (success) {
        this.emit('recoveryCompleted', plan.id, error.id, result);
        this.logger?.info(`Recovery plan completed successfully: ${plan.id}`);
      } else {
        this.emit('recoveryFailed', plan.id, error.id, result);
        this.logger?.warn(`Recovery plan failed: ${plan.id}`);
      }

      return result;

    } catch (executionError) {
      const result: RecoveryExecutionResult = {
        planId: plan.id,
        stepResults,
        success: false,
        duration: Date.now() - startTime,
        error: (executionError as Error).message,
        timestamp: new Date()
      };

      this.emit('recoveryFailed', plan.id, error.id, result);
      this.logger?.error(`Recovery plan execution failed: ${plan.id}`, executionError);
      return result;
    }
  }

  /**
   * Execute a single recovery step
   */
  private async executeStep(step: RecoveryStep, error: EnhancedError): Promise<StepExecutionResult> {
    const startTime = Date.now();

    this.emit('stepStarted', step.id, step.name);
    this.logger?.debug(`Executing recovery step: ${step.id} (${step.name})`);

    try {
      const output = await this.executeAction(step.action, error);
      
      const result: StepExecutionResult = {
        stepId: step.id,
        success: true,
        duration: Date.now() - startTime,
        output
      };

      this.emit('stepCompleted', step.id, result);
      return result;

    } catch (stepError) {
      const result: StepExecutionResult = {
        stepId: step.id,
        success: false,
        duration: Date.now() - startTime,
        error: (stepError as Error).message
      };

      this.emit('stepFailed', step.id, result);
      this.logger?.warn(`Recovery step failed: ${step.id}`, stepError);
      return result;
    }
  }

  /**
   * Execute a recovery action
   */
  private async executeAction(action: RecoveryAction, error: EnhancedError): Promise<any> {
    switch (action.type) {
      case 'restart_service':
        return await this.restartService(action.service);
      
      case 'clear_cache':
        return await this.clearCache(action.cache);
      
      case 'reset_connection':
        return await this.resetConnection(action.connection);
      
      case 'switch_provider':
        return await this.switchProvider(action.provider);
      
      case 'scale_resources':
        return await this.scaleResources(action.factor);
      
      case 'notify_admin':
        return await this.notifyAdmin(action.message, error);
      
      case 'execute_script':
        return await this.executeScript(action.script, action.args);
      
      case 'custom':
        return await this.executeCustomHandler(action.handler, action.params, error);
      
      default:
        throw new Error(`Unknown recovery action type: ${(action as any).type}`);
    }
  }

  /**
   * Find best recovery plan for an error
   */
  private findBestRecoveryPlan(error: EnhancedError): RecoveryPlan | undefined {
    const eligiblePlans = Array.from(this.recoveryPlans.values())
      .filter(plan => plan.enabled && this.matchesErrorPattern(plan.errorPattern, error))
      .sort((a, b) => {
        // Sort by priority (higher first) and success rate
        if (a.priority !== b.priority) {
          return b.priority - a.priority;
        }
        return b.successRate - a.successRate;
      });

    return eligiblePlans[0];
  }

  /**
   * Check if error matches plan pattern
   */
  private matchesErrorPattern(pattern: string, error: EnhancedError): boolean {
    try {
      const regex = new RegExp(pattern, 'i');
      return regex.test(error.originalError.message) ||
             regex.test(error.category) ||
             regex.test(error.context.component);
    } catch {
      return false;
    }
  }

  /**
   * Check if dependencies are met
   */
  private areDependenciesMet(dependencies: string[], executedSteps: Set<string>): boolean {
    return dependencies.every(dep => executedSteps.has(dep));
  }

  /**
   * Evaluate condition
   */
  private evaluateCondition(condition: string, error: EnhancedError): boolean {
    try {
      // Simple condition evaluation - replace with safer evaluator in production
      const context = {
        error: error.originalError.message,
        category: error.category,
        severity: error.severity,
        component: error.context.component,
        retryCount: error.retryCount
      };

      const evaluableCondition = condition.replace(/\$\{([^}]+)\}/g, (match, varName) => {
        const value = (context as any)[varName];
        return typeof value === 'string' ? `"${value}"` : String(value);
      });

      return new Function('return ' + evaluableCondition)();
    } catch {
      return false;
    }
  }

  /**
   * Update plan statistics
   */
  private updatePlanStatistics(plan: RecoveryPlan, success: boolean): void {
    plan.totalExecutions++;
    plan.lastUsed = new Date();
    
    const successCount = Math.round(plan.successRate * (plan.totalExecutions - 1)) + (success ? 1 : 0);
    plan.successRate = successCount / plan.totalExecutions;
  }

  /**
   * Recovery action implementations
   */
  private async restartService(service: string): Promise<any> {
    this.logger?.info(`Restarting service: ${service}`);
    // Implementation would depend on the service management system
    return { action: 'restart_service', service, status: 'simulated' };
  }

  private async clearCache(cache: string): Promise<any> {
    this.logger?.info(`Clearing cache: ${cache}`);
    // Implementation would clear the specified cache
    return { action: 'clear_cache', cache, status: 'simulated' };
  }

  private async resetConnection(connection: string): Promise<any> {
    this.logger?.info(`Resetting connection: ${connection}`);
    // Implementation would reset the specified connection
    return { action: 'reset_connection', connection, status: 'simulated' };
  }

  private async switchProvider(provider: string): Promise<any> {
    this.logger?.info(`Switching to provider: ${provider}`);
    // Implementation would switch to the specified provider
    return { action: 'switch_provider', provider, status: 'simulated' };
  }

  private async scaleResources(factor: number): Promise<any> {
    this.logger?.info(`Scaling resources by factor: ${factor}`);
    // Implementation would scale resources
    return { action: 'scale_resources', factor, status: 'simulated' };
  }

  private async notifyAdmin(message: string, error: EnhancedError): Promise<any> {
    this.logger?.warn(`Admin notification: ${message}`, { errorId: error.id });
    this.emit('adminNotification', message, error);
    return { action: 'notify_admin', message, sent: true };
  }

  private async executeScript(script: string, args?: string[]): Promise<any> {
    this.logger?.info(`Executing script: ${script}`, { args });
    // Implementation would execute the specified script
    return { action: 'execute_script', script, args, status: 'simulated' };
  }

  private async executeCustomHandler(
    handlerName: string,
    params: any,
    error: EnhancedError
  ): Promise<any> {
    const handler = this.customHandlers.get(handlerName);
    if (!handler) {
      throw new Error(`Custom handler not found: ${handlerName}`);
    }

    return await handler.execute(params, error);
  }

  /**
   * Setup default recovery plans
   */
  private setupDefaultPlans(): void {
    // Network error recovery
    this.registerRecoveryPlan({
      id: 'network-error-recovery',
      name: 'Network Error Recovery',
      description: 'Recovery plan for network-related errors',
      errorPattern: 'network|connection|timeout',
      priority: 8,
      enabled: true,
      successRate: 0,
      totalExecutions: 0,
      steps: [
        {
          id: 'wait-and-retry',
          name: 'Wait and Retry',
          description: 'Wait for a short period and retry the operation',
          type: 'automatic',
          action: { type: 'custom', handler: 'wait-retry', params: { delay: 5000 } }
        },
        {
          id: 'reset-connection',
          name: 'Reset Connection',
          description: 'Reset the network connection',
          type: 'automatic',
          action: { type: 'reset_connection', connection: 'default' }
        }
      ]
    });

    // Rate limit recovery
    this.registerRecoveryPlan({
      id: 'rate-limit-recovery',
      name: 'Rate Limit Recovery',
      description: 'Recovery plan for rate limiting errors',
      errorPattern: 'rate.?limit|too.?many.?requests',
      priority: 9,
      enabled: true,
      successRate: 0,
      totalExecutions: 0,
      steps: [
        {
          id: 'exponential-backoff',
          name: 'Exponential Backoff',
          description: 'Wait with exponential backoff',
          type: 'automatic',
          action: { type: 'custom', handler: 'exponential-backoff' }
        },
        {
          id: 'switch-provider',
          name: 'Switch Provider',
          description: 'Switch to alternative provider if available',
          type: 'conditional',
          condition: '${retryCount} > 3',
          action: { type: 'switch_provider', provider: 'fallback' }
        }
      ]
    });

    // Authentication error recovery
    this.registerRecoveryPlan({
      id: 'auth-error-recovery',
      name: 'Authentication Error Recovery',
      description: 'Recovery plan for authentication errors',
      errorPattern: 'auth|unauthorized|forbidden',
      priority: 7,
      enabled: true,
      successRate: 0,
      totalExecutions: 0,
      steps: [
        {
          id: 'refresh-token',
          name: 'Refresh Authentication Token',
          description: 'Attempt to refresh the authentication token',
          type: 'automatic',
          action: { type: 'custom', handler: 'refresh-auth-token' }
        },
        {
          id: 'notify-admin',
          name: 'Notify Administrator',
          description: 'Notify administrator of authentication failure',
          type: 'manual',
          action: { type: 'notify_admin', message: 'Authentication error requires manual intervention' }
        }
      ]
    });

    this.logger?.info('Default recovery plans registered');
  }

  /**
   * Get recovery statistics
   */
  public getRecoveryStatistics(): any {
    const plans = Array.from(this.recoveryPlans.values());
    const recentExecutions = this.executionHistory.filter(
      e => Date.now() - e.timestamp.getTime() < 3600000 // Last hour
    );

    return {
      totalPlans: plans.length,
      enabledPlans: plans.filter(p => p.enabled).length,
      totalExecutions: this.executionHistory.length,
      recentExecutions: recentExecutions.length,
      successfulExecutions: this.executionHistory.filter(e => e.success).length,
      overallSuccessRate: this.executionHistory.length > 0 
        ? this.executionHistory.filter(e => e.success).length / this.executionHistory.length 
        : 0,
      planStatistics: plans.map(p => ({
        id: p.id,
        name: p.name,
        totalExecutions: p.totalExecutions,
        successRate: p.successRate,
        lastUsed: p.lastUsed
      }))
    };
  }

  /**
   * Get active recoveries
   */
  public getActiveRecoveries(): string[] {
    return Array.from(this.activeRecoveries.keys());
  }
}

/**
 * Custom recovery handler interface
 */
export interface RecoveryHandler {
  execute(params: any, error: EnhancedError): Promise<any>;
}
