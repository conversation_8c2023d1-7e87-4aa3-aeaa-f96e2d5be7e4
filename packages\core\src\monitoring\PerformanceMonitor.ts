/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { EventEmitter } from 'events';
import type { Logger } from '@inkbytefo/s647-shared';
import { MetricsCollector, type PerformanceMetric, type AgentMetric } from './MetricsCollector.js';

/**
 * Performance threshold
 */
export interface PerformanceThreshold {
  metric: string;
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  value: number;
  severity: 'info' | 'warning' | 'error' | 'critical';
  description: string;
}

/**
 * Performance alert
 */
export interface PerformanceAlert {
  id: string;
  threshold: PerformanceThreshold;
  currentValue: number;
  timestamp: Date;
  component?: string;
  operation?: string;
  metadata?: Record<string, any>;
}

/**
 * Performance trend
 */
export interface PerformanceTrend {
  metric: string;
  direction: 'up' | 'down' | 'stable';
  change: number;
  changePercent: number;
  timeWindow: number;
  confidence: number;
}

/**
 * Performance report
 */
export interface PerformanceReport {
  id: string;
  timestamp: Date;
  timeWindow: {
    start: Date;
    end: Date;
  };
  summary: {
    totalOperations: number;
    successRate: number;
    averageResponseTime: number;
    errorRate: number;
    throughput: number;
  };
  componentMetrics: ComponentMetrics[];
  agentMetrics: AgentPerformanceMetrics[];
  alerts: PerformanceAlert[];
  trends: PerformanceTrend[];
  recommendations: string[];
}

/**
 * Component metrics
 */
export interface ComponentMetrics {
  component: string;
  operations: number;
  successRate: number;
  averageResponseTime: number;
  errorRate: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
}

/**
 * Agent performance metrics
 */
export interface AgentPerformanceMetrics {
  agentId: string;
  operations: number;
  successRate: number;
  averageResponseTime: number;
  totalTokensUsed: number;
  totalCost: number;
  averageTokensPerOperation: number;
  averageCostPerOperation: number;
  errorRate: number;
}

/**
 * Performance monitor configuration
 */
export interface PerformanceMonitorConfig {
  enabled: boolean;
  alertingEnabled: boolean;
  reportInterval: number;
  trendAnalysisWindow: number;
  thresholds: PerformanceThreshold[];
  retentionPeriod: number;
}

/**
 * Performance monitor for tracking and analyzing system performance
 */
export class PerformanceMonitor extends EventEmitter {
  private config: PerformanceMonitorConfig;
  private metricsCollector: MetricsCollector;
  private logger?: Logger;
  private alerts: PerformanceAlert[] = [];
  private reports: PerformanceReport[] = [];
  private reportTimer?: NodeJS.Timeout;
  private lastReportTime: Date = new Date();

  constructor(
    metricsCollector: MetricsCollector,
    config: Partial<PerformanceMonitorConfig> = {},
    logger?: Logger
  ) {
    super();
    this.metricsCollector = metricsCollector;
    this.logger = logger;
    this.config = {
      enabled: true,
      alertingEnabled: true,
      reportInterval: 300000, // 5 minutes
      trendAnalysisWindow: 3600000, // 1 hour
      retentionPeriod: 86400000, // 24 hours
      thresholds: this.getDefaultThresholds(),
      ...config
    };

    if (this.config.enabled) {
      this.startMonitoring();
    }
  }

  /**
   * Start performance monitoring
   */
  public startMonitoring(): void {
    if (!this.config.enabled) return;

    // Listen to metrics events
    this.metricsCollector.on('performanceMetric', (metric: PerformanceMetric) => {
      this.analyzePerformanceMetric(metric);
    });

    this.metricsCollector.on('agentMetric', (metric: AgentMetric) => {
      this.analyzeAgentMetric(metric);
    });

    // Start report generation
    this.reportTimer = setInterval(() => {
      this.generateReport();
    }, this.config.reportInterval);

    this.logger?.info('Performance monitoring started');
  }

  /**
   * Stop performance monitoring
   */
  public stopMonitoring(): void {
    if (this.reportTimer) {
      clearInterval(this.reportTimer);
      this.reportTimer = undefined;
    }

    this.logger?.info('Performance monitoring stopped');
  }

  /**
   * Analyze performance metric
   */
  private analyzePerformanceMetric(metric: PerformanceMetric): void {
    // Check thresholds
    this.checkThresholds(metric);

    // Update real-time metrics
    this.metricsCollector.recordPerformance(metric);
  }

  /**
   * Analyze agent metric
   */
  private analyzeAgentMetric(metric: AgentMetric): void {
    // Check agent-specific thresholds
    this.checkAgentThresholds(metric);

    // Update real-time metrics
    this.metricsCollector.recordAgent(metric);
  }

  /**
   * Check performance thresholds
   */
  private checkThresholds(metric: PerformanceMetric): void {
    if (!this.config.alertingEnabled) return;

    for (const threshold of this.config.thresholds) {
      const value = this.extractMetricValue(metric, threshold.metric);
      if (value === undefined) continue;

      if (this.evaluateThreshold(value, threshold)) {
        const alert: PerformanceAlert = {
          id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          threshold,
          currentValue: value,
          timestamp: new Date(),
          component: metric.component,
          operation: metric.operation,
          metadata: metric.metadata
        };

        this.alerts.push(alert);
        this.emit('alert', alert);
        this.logger?.warn(`Performance alert: ${threshold.description}`, alert);
      }
    }
  }

  /**
   * Check agent-specific thresholds
   */
  private checkAgentThresholds(metric: AgentMetric): void {
    if (!this.config.alertingEnabled) return;

    // Agent-specific threshold checks
    const agentThresholds: PerformanceThreshold[] = [
      {
        metric: 'duration',
        operator: 'gt',
        value: 30000, // 30 seconds
        severity: 'warning',
        description: 'Agent operation taking too long'
      },
      {
        metric: 'tokensUsed',
        operator: 'gt',
        value: 10000,
        severity: 'warning',
        description: 'High token usage detected'
      },
      {
        metric: 'cost',
        operator: 'gt',
        value: 1.0,
        severity: 'warning',
        description: 'High cost per operation'
      }
    ];

    for (const threshold of agentThresholds) {
      const value = this.extractAgentMetricValue(metric, threshold.metric);
      if (value === undefined) continue;

      if (this.evaluateThreshold(value, threshold)) {
        const alert: PerformanceAlert = {
          id: `agent-alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          threshold,
          currentValue: value,
          timestamp: new Date(),
          component: 'agent',
          operation: metric.operation,
          metadata: { agentId: metric.agentId, ...metric.metadata }
        };

        this.alerts.push(alert);
        this.emit('agentAlert', alert);
        this.logger?.warn(`Agent performance alert: ${threshold.description}`, alert);
      }
    }
  }

  /**
   * Extract metric value
   */
  private extractMetricValue(metric: PerformanceMetric, metricName: string): number | undefined {
    switch (metricName) {
      case 'duration':
        return metric.duration;
      case 'success':
        return metric.success ? 1 : 0;
      default:
        return metric.metadata?.[metricName];
    }
  }

  /**
   * Extract agent metric value
   */
  private extractAgentMetricValue(metric: AgentMetric, metricName: string): number | undefined {
    switch (metricName) {
      case 'duration':
        return metric.duration;
      case 'tokensUsed':
        return metric.tokensUsed;
      case 'cost':
        return metric.cost;
      case 'success':
        return metric.success ? 1 : 0;
      default:
        return metric.metadata?.[metricName];
    }
  }

  /**
   * Evaluate threshold
   */
  private evaluateThreshold(value: number, threshold: PerformanceThreshold): boolean {
    switch (threshold.operator) {
      case 'gt':
        return value > threshold.value;
      case 'lt':
        return value < threshold.value;
      case 'eq':
        return value === threshold.value;
      case 'gte':
        return value >= threshold.value;
      case 'lte':
        return value <= threshold.value;
      default:
        return false;
    }
  }

  /**
   * Generate performance report
   */
  private generateReport(): void {
    const now = new Date();
    const reportStart = this.lastReportTime;
    const reportEnd = now;

    try {
      const report = this.createPerformanceReport(reportStart, reportEnd);
      this.reports.push(report);
      this.lastReportTime = now;

      // Clean old reports
      this.cleanOldReports();

      this.emit('report', report);
      this.logger?.info(`Performance report generated: ${report.id}`);

    } catch (error) {
      this.logger?.error('Failed to generate performance report:', error);
    }
  }

  /**
   * Create performance report
   */
  private createPerformanceReport(start: Date, end: Date): PerformanceReport {
    const snapshot = this.metricsCollector.getMetricsSnapshot();
    const rangeData = this.metricsCollector.getMetricsInRange(start, end);

    // Calculate summary metrics
    const totalOperations = rangeData.performanceMetrics.length + rangeData.agentMetrics.length;
    const successfulOps = rangeData.performanceMetrics.filter(m => m.success).length +
                         rangeData.agentMetrics.filter(m => m.success).length;
    const successRate = totalOperations > 0 ? successfulOps / totalOperations : 0;

    const allDurations = [
      ...rangeData.performanceMetrics.map(m => m.duration),
      ...rangeData.agentMetrics.map(m => m.duration)
    ];
    const averageResponseTime = allDurations.length > 0 
      ? allDurations.reduce((a, b) => a + b, 0) / allDurations.length 
      : 0;

    const errorRate = 1 - successRate;
    const timeWindowMs = end.getTime() - start.getTime();
    const throughput = totalOperations / (timeWindowMs / 1000); // ops per second

    // Calculate component metrics
    const componentMetrics = this.calculateComponentMetrics(rangeData.performanceMetrics);

    // Calculate agent metrics
    const agentMetrics = this.calculateAgentMetrics(rangeData.agentMetrics);

    // Get recent alerts
    const recentAlerts = this.alerts.filter(a => a.timestamp >= start && a.timestamp <= end);

    // Analyze trends
    const trends = this.analyzeTrends();

    // Generate recommendations
    const recommendations = this.generateRecommendations(
      { successRate, averageResponseTime, errorRate, throughput },
      componentMetrics,
      agentMetrics,
      recentAlerts
    );

    return {
      id: `report-${Date.now()}`,
      timestamp: new Date(),
      timeWindow: { start, end },
      summary: {
        totalOperations,
        successRate,
        averageResponseTime,
        errorRate,
        throughput
      },
      componentMetrics,
      agentMetrics,
      alerts: recentAlerts,
      trends,
      recommendations
    };
  }

  /**
   * Calculate component metrics
   */
  private calculateComponentMetrics(performanceMetrics: PerformanceMetric[]): ComponentMetrics[] {
    const componentGroups = new Map<string, PerformanceMetric[]>();

    for (const metric of performanceMetrics) {
      if (!componentGroups.has(metric.component)) {
        componentGroups.set(metric.component, []);
      }
      componentGroups.get(metric.component)!.push(metric);
    }

    return Array.from(componentGroups.entries()).map(([component, metrics]) => {
      const operations = metrics.length;
      const successfulOps = metrics.filter(m => m.success).length;
      const successRate = operations > 0 ? successfulOps / operations : 0;
      const durations = metrics.map(m => m.duration).sort((a, b) => a - b);
      const averageResponseTime = durations.length > 0 
        ? durations.reduce((a, b) => a + b, 0) / durations.length 
        : 0;
      const errorRate = 1 - successRate;
      const p95ResponseTime = durations.length > 0 
        ? durations[Math.floor(durations.length * 0.95)] 
        : 0;
      const p99ResponseTime = durations.length > 0 
        ? durations[Math.floor(durations.length * 0.99)] 
        : 0;

      return {
        component,
        operations,
        successRate,
        averageResponseTime,
        errorRate,
        p95ResponseTime,
        p99ResponseTime
      };
    });
  }

  /**
   * Calculate agent metrics
   */
  private calculateAgentMetrics(agentMetrics: AgentMetric[]): AgentPerformanceMetrics[] {
    const agentGroups = new Map<string, AgentMetric[]>();

    for (const metric of agentMetrics) {
      if (!agentGroups.has(metric.agentId)) {
        agentGroups.set(metric.agentId, []);
      }
      agentGroups.get(metric.agentId)!.push(metric);
    }

    return Array.from(agentGroups.entries()).map(([agentId, metrics]) => {
      const operations = metrics.length;
      const successfulOps = metrics.filter(m => m.success).length;
      const successRate = operations > 0 ? successfulOps / operations : 0;
      const durations = metrics.map(m => m.duration);
      const averageResponseTime = durations.length > 0 
        ? durations.reduce((a, b) => a + b, 0) / durations.length 
        : 0;
      const totalTokensUsed = metrics.reduce((sum, m) => sum + (m.tokensUsed || 0), 0);
      const totalCost = metrics.reduce((sum, m) => sum + (m.cost || 0), 0);
      const averageTokensPerOperation = operations > 0 ? totalTokensUsed / operations : 0;
      const averageCostPerOperation = operations > 0 ? totalCost / operations : 0;
      const errorRate = 1 - successRate;

      return {
        agentId,
        operations,
        successRate,
        averageResponseTime,
        totalTokensUsed,
        totalCost,
        averageTokensPerOperation,
        averageCostPerOperation,
        errorRate
      };
    });
  }

  /**
   * Analyze performance trends
   */
  private analyzeTrends(): PerformanceTrend[] {
    // Simplified trend analysis
    // In production, implement more sophisticated trend detection
    return [
      {
        metric: 'response_time',
        direction: 'stable',
        change: 0,
        changePercent: 0,
        timeWindow: this.config.trendAnalysisWindow,
        confidence: 0.8
      }
    ];
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(
    summary: any,
    componentMetrics: ComponentMetrics[],
    agentMetrics: AgentPerformanceMetrics[],
    alerts: PerformanceAlert[]
  ): string[] {
    const recommendations: string[] = [];

    // Success rate recommendations
    if (summary.successRate < 0.95) {
      recommendations.push('Success rate is below 95%. Consider investigating error patterns and implementing better error handling.');
    }

    // Response time recommendations
    if (summary.averageResponseTime > 5000) {
      recommendations.push('Average response time is high. Consider optimizing slow operations or implementing caching.');
    }

    // Component-specific recommendations
    for (const component of componentMetrics) {
      if (component.errorRate > 0.1) {
        recommendations.push(`Component ${component.component} has high error rate (${(component.errorRate * 100).toFixed(1)}%). Investigate and fix issues.`);
      }
      if (component.p95ResponseTime > 10000) {
        recommendations.push(`Component ${component.component} has high P95 response time. Consider performance optimization.`);
      }
    }

    // Agent-specific recommendations
    for (const agent of agentMetrics) {
      if (agent.averageCostPerOperation > 0.1) {
        recommendations.push(`Agent ${agent.agentId} has high cost per operation. Consider optimizing prompts or using more efficient models.`);
      }
      if (agent.averageTokensPerOperation > 5000) {
        recommendations.push(`Agent ${agent.agentId} uses many tokens per operation. Consider prompt optimization.`);
      }
    }

    // Alert-based recommendations
    if (alerts.length > 10) {
      recommendations.push('High number of alerts detected. Review and adjust alert thresholds or address underlying issues.');
    }

    return recommendations;
  }

  /**
   * Clean old reports
   */
  private cleanOldReports(): void {
    const cutoff = new Date(Date.now() - this.config.retentionPeriod);
    this.reports = this.reports.filter(r => r.timestamp >= cutoff);
    this.alerts = this.alerts.filter(a => a.timestamp >= cutoff);
  }

  /**
   * Get default thresholds
   */
  private getDefaultThresholds(): PerformanceThreshold[] {
    return [
      {
        metric: 'duration',
        operator: 'gt',
        value: 10000, // 10 seconds
        severity: 'warning',
        description: 'Operation duration exceeds 10 seconds'
      },
      {
        metric: 'duration',
        operator: 'gt',
        value: 30000, // 30 seconds
        severity: 'error',
        description: 'Operation duration exceeds 30 seconds'
      },
      {
        metric: 'success',
        operator: 'eq',
        value: 0,
        severity: 'error',
        description: 'Operation failed'
      }
    ];
  }

  /**
   * Get current alerts
   */
  public getCurrentAlerts(): PerformanceAlert[] {
    return [...this.alerts];
  }

  /**
   * Get recent reports
   */
  public getRecentReports(limit: number = 10): PerformanceReport[] {
    return this.reports.slice(-limit);
  }

  /**
   * Get latest report
   */
  public getLatestReport(): PerformanceReport | undefined {
    return this.reports[this.reports.length - 1];
  }

  /**
   * Add custom threshold
   */
  public addThreshold(threshold: PerformanceThreshold): void {
    this.config.thresholds.push(threshold);
    this.logger?.debug(`Added performance threshold: ${threshold.description}`);
  }

  /**
   * Remove threshold
   */
  public removeThreshold(description: string): boolean {
    const index = this.config.thresholds.findIndex(t => t.description === description);
    if (index >= 0) {
      this.config.thresholds.splice(index, 1);
      this.logger?.debug(`Removed performance threshold: ${description}`);
      return true;
    }
    return false;
  }
}
